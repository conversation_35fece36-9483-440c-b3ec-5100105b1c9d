import React, { useState } from 'react';
import { HomeIcon, FolderIcon, ArchiveBoxIcon, QuestionMarkCircleIcon, Cog6ToothIcon, ArrowLeftOnRectangleIcon, Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';

const NavLink = ({ icon, label, href, active, setActive, setIsSidebarOpen }) => {
  const Icon = icon;
  const isActive = active === href;
  return (
    <a
      href="#"
      onClick={(e) => { 
        e.preventDefault(); 
        setActive(href); 
        if (setIsSidebarOpen) {
          setIsSidebarOpen(false);
        }
      }}
      className={`flex items-center px-3 py-3 text-sm font-medium rounded-md transition-colors duration-150
        ${isActive
          ? 'bg-white text-brand-purple font-bold'
          : 'text-white hover:bg-white/20'}
      `}
    >
      <Icon className={`h-6 w-6 mr-3 ${isActive ? 'text-brand-purple' : 'text-white'}`} />
      <span>{label}</span>
    </a>
  );
};

export default function AdminLayout({ children, active, setActive, onLogout }) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const navItems = [
    { href: 'dashboard', label: 'Dashboard', icon: HomeIcon },
    { href: 'reports', label: 'Reports', icon: FolderIcon },
    { href: 'archive', label: 'Archived', icon: ArchiveBoxIcon },
    { href: 'questions', label: 'Questions', icon: QuestionMarkCircleIcon },
    { href: 'settings', label: 'Settings', icon: Cog6ToothIcon },
  ];

  return (
    <div className="flex h-screen bg-gray-100 dark:bg-gray-900">
      {/* Sidebar */}
      <aside className={`w-64 flex-shrink-0 bg-brand-purple flex flex-col fixed inset-y-0 left-0 z-30 transform ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full'} md:relative md:translate-x-0 transition-transform duration-300 ease-in-out`}>
        <div className="h-16 flex items-center justify-between px-4 border-b border-white/20">
          <h1 className="text-2xl font-bold text-white">Inspection Admin</h1>
          <button onClick={() => setIsSidebarOpen(false)} className="md:hidden text-white">
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>
        <nav className="flex-1 p-4 space-y-2">
          {navItems.map(item => (
            <NavLink
              key={item.href}
              {...item}
              active={active}
              setActive={setActive}
              setIsSidebarOpen={setIsSidebarOpen}
            />
          ))}
        </nav>
        <div className="p-4 border-t border-white/20">
          <button
            onClick={onLogout}
            className="w-full flex items-center px-3 py-3 text-sm font-medium rounded-md text-white hover:bg-white/20 transition-colors duration-150"
          >
            <ArrowLeftOnRectangleIcon className="h-6 w-6 mr-3" />
            <span>Logout</span>
          </button>
        </div>
      </aside>

      {isSidebarOpen && (
        <div 
          className="fixed inset-0 bg-black opacity-50 z-20 md:hidden"
          onClick={() => setIsSidebarOpen(false)}
        ></div>
      )}

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="h-16 bg-white/80 dark:bg-sidebar-dark/90 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between px-6 backdrop-blur-md">
          <div className="flex items-center">
            <button 
              className="md:hidden mr-4 text-gray-800 dark:text-gray-200"
              onClick={() => setIsSidebarOpen(true)}
            >
              <Bars3Icon className="h-6 w-6" />
            </button>
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 capitalize">{active}</h2>
          </div>
          {/* Header content can go here */}
        </header>
        <main className="flex-1 overflow-y-auto p-6 bg-white/80 dark:bg-sidebar-dark/90 backdrop-blur-md">
          {children}
        </main>
      </div>
    </div>
  );
} 