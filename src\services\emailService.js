import { getSettingsFromFirestore } from './settingsService';
import { getReportsFromFirestore } from './reportsService';

/**
 * Email service for sending inspection reports
 * This service handles email functionality for the inspection app
 */

/**
 * Send inspection report via email
 * @param {Object} reportData - Report data to send
 * @param {string} recipientEmail - Email address to send to (optional, uses default if not provided)
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} Success status
 */
export const sendReportByEmail = async (reportData, recipientEmail = null, options = {}) => {
  try {
    // Get settings for email configuration
    const settingsResult = await getSettingsFromFirestore();
    if (!settingsResult.success) {
      throw new Error("Failed to load email settings");
    }

    const { delivery, integrations } = settingsResult.data;
    const emailConfig = integrations.email;
    
    // Use provided email or default from settings
    const toEmail = recipientEmail || delivery.defaultEmail;
    
    if (!toEmail) {
      throw new Error("No recipient email address provided");
    }

    // Prepare email data
    const emailData = {
      to: toEmail,
      from: {
        email: emailConfig.fromEmail || '<EMAIL>',
        name: emailConfig.fromName || 'Inspection Reports'
      },
      subject: `Inspection Report - ${reportData.reportName || 'Property Inspection'}`,
      html: generateEmailTemplate(reportData, delivery.emailTemplate),
      attachments: options.includePDF ? await generatePDFAttachment(reportData) : []
    };

    // For demo purposes, we'll simulate email sending
    // In production, you would integrate with a real email service like:
    // - SendGrid
    // - AWS SES
    // - Nodemailer with SMTP
    // - Firebase Functions with email service

    console.log('📧 Sending email:', emailData);
    
    // Simulate email sending delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Log email activity
    await logEmailActivity({
      reportId: reportData.id,
      recipientEmail: toEmail,
      subject: emailData.subject,
      status: 'sent',
      timestamp: new Date().toISOString()
    });

    return {
      success: true,
      message: `Report sent successfully to ${toEmail}`,
      data: {
        emailId: `email_${Date.now()}`,
        recipient: toEmail,
        subject: emailData.subject
      }
    };

  } catch (error) {
    console.error("Error sending email:", error);
    
    // Log failed email activity
    await logEmailActivity({
      reportId: reportData?.id || 'unknown',
      recipientEmail: recipientEmail || 'unknown',
      status: 'failed',
      error: error.message,
      timestamp: new Date().toISOString()
    });

    return {
      success: false,
      error: error.code || "email-send-error",
      message: error.message || "Failed to send email"
    };
  }
};

/**
 * Send multiple reports via email
 * @param {Array} reportIds - Array of report IDs to send
 * @param {string} recipientEmail - Email address to send to
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} Success status with results
 */
export const sendMultipleReportsByEmail = async (reportIds, recipientEmail, options = {}) => {
  try {
    const results = [];
    const reportsResult = await getReportsFromFirestore();
    
    if (!reportsResult.success) {
      throw new Error("Failed to load reports");
    }

    const reports = reportsResult.data.filter(report => reportIds.includes(report.id));
    
    for (const report of reports) {
      const result = await sendReportByEmail(report, recipientEmail, options);
      results.push({
        reportId: report.id,
        reportName: report.reportName,
        success: result.success,
        message: result.message
      });
    }

    const successCount = results.filter(r => r.success).length;
    
    return {
      success: successCount > 0,
      message: `${successCount}/${results.length} reports sent successfully`,
      data: {
        results: results,
        successCount: successCount,
        totalCount: results.length
      }
    };

  } catch (error) {
    console.error("Error sending multiple emails:", error);
    return {
      success: false,
      error: error.code || "bulk-email-error",
      message: error.message || "Failed to send emails"
    };
  }
};

/**
 * Generate HTML email template
 * @param {Object} reportData - Report data
 * @param {string} templateType - Template type (default, detailed, summary)
 * @returns {string} HTML email content
 */
const generateEmailTemplate = (reportData, templateType = 'default') => {
  const baseTemplate = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Inspection Report</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #6366f1; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
        .report-info { background: white; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .label { font-weight: bold; color: #6366f1; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Inspection Report</h1>
        </div>
        <div class="content">
          <div class="report-info">
            <p><span class="label">Report Name:</span> ${reportData.reportName || 'N/A'}</p>
            <p><span class="label">Inspector:</span> ${reportData.userName || 'N/A'}</p>
            <p><span class="label">Property:</span> ${reportData.property || 'N/A'}</p>
            <p><span class="label">Date:</span> ${reportData.date || 'N/A'}</p>
            <p><span class="label">Status:</span> ${reportData.status || 'N/A'}</p>
          </div>
          
          ${templateType === 'detailed' ? generateDetailedContent(reportData) : ''}
          
          <p>This inspection report has been generated automatically. Please review the attached documents for complete details.</p>
          
          ${reportData.photos && reportData.photos.length > 0 ? 
            `<p><span class="label">Photos:</span> ${reportData.photos.length} photo(s) included</p>` : ''
          }
        </div>
        <div class="footer">
          <p>Generated by Inspection App | ${new Date().toLocaleDateString()}</p>
          <p>This is an automated message. Please do not reply to this email.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  return baseTemplate;
};

/**
 * Generate detailed content for email template
 * @param {Object} reportData - Report data
 * @returns {string} Detailed HTML content
 */
const generateDetailedContent = (reportData) => {
  if (!reportData.questionnaireData) {
    return '<p>No detailed questionnaire data available.</p>';
  }

  const qData = reportData.questionnaireData;
  return `
    <div class="report-info">
      <h3>Inspection Details</h3>
      ${qData.inspectorName ? `<p><span class="label">Inspector Name:</span> ${qData.inspectorName}</p>` : ''}
      ${qData.droneNumber ? `<p><span class="label">Drone Number:</span> ${qData.droneNumber}</p>` : ''}
      ${qData.policyNumber ? `<p><span class="label">Policy Number:</span> ${qData.policyNumber}</p>` : ''}
      ${qData.insuredName ? `<p><span class="label">Insured Name:</span> ${qData.insuredName}</p>` : ''}
      ${qData.insuredStreetAddress ? `<p><span class="label">Address:</span> ${qData.insuredStreetAddress}</p>` : ''}
      ${qData.insuredState ? `<p><span class="label">State:</span> ${qData.insuredState}</p>` : ''}
      ${qData.insuredZipCode ? `<p><span class="label">Zip Code:</span> ${qData.insuredZipCode}</p>` : ''}
    </div>
  `;
};

/**
 * Generate PDF attachment for email
 * @param {Object} reportData - Report data
 * @returns {Promise<Object>} PDF attachment object
 */
const generatePDFAttachment = async (reportData) => {
  // This would integrate with the existing PDF generation functionality
  // For now, return a placeholder
  return {
    filename: `inspection-report-${reportData.id || 'unknown'}.pdf`,
    content: 'PDF content would be generated here',
    contentType: 'application/pdf'
  };
};

/**
 * Log email activity to Firestore
 * @param {Object} activityData - Email activity data
 * @returns {Promise<void>}
 */
const logEmailActivity = async (activityData) => {
  try {
    // In a real implementation, you would save this to Firestore
    console.log('📝 Email activity logged:', activityData);
  } catch (error) {
    console.error("Error logging email activity:", error);
  }
};

/**
 * Test email configuration
 * @returns {Promise<Object>} Test result
 */
export const testEmailConfiguration = async () => {
  try {
    const settingsResult = await getSettingsFromFirestore();
    if (!settingsResult.success) {
      throw new Error("Failed to load email settings");
    }

    const { delivery, integrations } = settingsResult.data;
    const emailConfig = integrations.email;

    // Validate email configuration
    const issues = [];
    
    if (!delivery.defaultEmail) {
      issues.push("Default email address not configured");
    }
    
    if (!emailConfig.fromEmail) {
      issues.push("From email address not configured");
    }

    if (issues.length > 0) {
      return {
        success: false,
        message: "Email configuration issues found",
        issues: issues
      };
    }

    // Send test email
    const testResult = await sendReportByEmail({
      id: 'test',
      reportName: 'Test Report',
      userName: 'Test Inspector',
      property: 'Test Property',
      date: new Date().toISOString(),
      status: 'Test'
    }, delivery.defaultEmail, { isTest: true });

    return {
      success: testResult.success,
      message: testResult.success ? 
        "Email configuration test successful" : 
        "Email configuration test failed",
      details: testResult
    };

  } catch (error) {
    console.error("Error testing email configuration:", error);
    return {
      success: false,
      error: error.code || "test-email-error",
      message: error.message || "Failed to test email configuration"
    };
  }
};
