import { useState, useEffect } from 'react';
import {
  getReportsFromFirestore,
  getReportFromFirestore,
  updateReportInFirestore,
  archiveReport,
  unarchiveReport,
  getArchivedReports
} from '../services/reportsService';

/**
 * Custom hook for managing reports data with Firebase Firestore
 * @returns {Object} Reports state and management functions
 */
export const useReports = () => {
  const [reports, setReports] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [saving, setSaving] = useState(false);

  /**
   * Load reports from Firestore
   */
  const loadReports = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await getReportsFromFirestore();
      
      if (result.success) {
        setReports(result.data);
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      console.error('Error loading reports:', err);
      setError(err.message || 'Failed to load reports');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Get a single report by ID
   * @param {string} reportId - Report ID
   * @returns {Promise<Object>} Report data or error
   */
  const getReport = async (reportId) => {
    try {
      const result = await getReportFromFirestore(reportId);
      
      if (result.success) {
        return { success: true, data: result.data };
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      console.error('Error getting report:', err);
      return { success: false, message: err.message || 'Failed to get report' };
    }
  };

  /**
   * Update a report
   * @param {string} reportId - Report ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} Success status
   */
  const updateReport = async (reportId, updateData) => {
    try {
      setSaving(true);
      setError(null);

      const result = await updateReportInFirestore(reportId, updateData);

      if (result.success) {
        // Update local state
        setReports(prevReports => 
          prevReports.map(report => 
            report.id === reportId 
              ? { ...report, ...updateData }
              : report
          )
        );
        return { success: true, message: result.message };
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      console.error('Error updating report:', err);
      const errorMessage = err.message || 'Failed to update report';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setSaving(false);
    }
  };



  /**
   * Archive a report (change status to 'Archived')
   * @param {string} reportId - Report ID
   * @returns {Promise<Object>} Success status
   */
  const archiveReportById = async (reportId) => {
    try {
      setSaving(true);
      setError(null);

      const result = await archiveReport(reportId);

      if (result.success) {
        // Update local state
        setReports(prevReports => 
          prevReports.map(report => 
            report.id === reportId 
              ? { ...report, status: 'Archived', archivedAt: new Date().toISOString() }
              : report
          )
        );
        return { success: true, message: result.message };
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      console.error('Error archiving report:', err);
      const errorMessage = err.message || 'Failed to archive report';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setSaving(false);
    }
  };

  /**
   * Unarchive a report (change status back to 'Active')
   * @param {string} reportId - Report ID
   * @returns {Promise<Object>} Success status
   */
  const unarchiveReportById = async (reportId) => {
    try {
      setSaving(true);
      setError(null);

      const result = await unarchiveReport(reportId);

      if (result.success) {
        // Update local state
        setReports(prevReports => 
          prevReports.map(report => 
            report.id === reportId 
              ? { ...report, status: 'Active', unarchivedAt: new Date().toISOString() }
              : report
          )
        );
        return { success: true, message: result.message };
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      console.error('Error unarchiving report:', err);
      const errorMessage = err.message || 'Failed to unarchive report';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setSaving(false);
    }
  };

  /**
   * Load archived reports specifically
   * @returns {Promise<Object>} Archived reports data
   */
  const loadArchivedReports = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await getArchivedReports();
      
      if (result.success) {
        return { success: true, data: result.data };
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      console.error('Error loading archived reports:', err);
      setError(err.message || 'Failed to load archived reports');
      return { success: false, message: err.message || 'Failed to load archived reports' };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Refresh reports from Firestore
   */
  const refreshReports = async () => {
    await loadReports();
  };

  // Load reports on hook initialization
  useEffect(() => {
    loadReports();
  }, []);

  return {
    // State
    reports,
    loading,
    error,
    saving,
    
    // Actions
    getReport,
    updateReport,
    archiveReportById,
    unarchiveReportById,
    loadArchivedReports,
    refreshReports,

    // Direct state setters (for local state management)
    setReports,
    setError
  };
};
