import React, { useState } from 'react';
import <PERSON> from 'papapar<PERSON>';
import jsPDF from 'jspdf';
import { ArrowDownTrayIcon, DocumentArrowDownIcon } from '@heroicons/react/24/outline';
import { useReports } from '../../hooks/useReports';

export default function ExportArchive() {
  const { reports, loading, error } = useReports();
  const [userName, setUserName] = useState('');
  const [status, setStatus] = useState('');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');

  // Handle loading and error states
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading reports...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-red-800 mb-2">Error Loading Reports</h3>
        <p className="text-red-600 mb-4">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
        >
          Retry
        </button>
      </div>
    );
  }

  const inspectors = Array.from(new Set(reports.map(r => r.userName)));
  const statuses = Array.from(new Set(reports.map(r => r.status)));
  const archivedReports = reports.filter(r => r.status === 'Archived');

  const filtered = reports.filter(r => {
    const date = r.date.split(' ')[0];
    const afterFrom = !dateFrom || date >= dateFrom;
    const beforeTo = !dateTo || date <= dateTo;
    const matchUser = !userName || r.userName === userName;
    const matchStatus = !status || r.status === status;
    return afterFrom && beforeTo && matchUser && matchStatus;
  });

  function exportCSV() {
    const flat = filtered.map(r => ({
      id: r.id,
      reportName: r.reportName,
      userName: r.userName,
      property: r.property,
      date: r.date,
      status: r.status,
      inspectorName: r.questionnaireData?.inspectorName || '',
      droneNumber: r.questionnaireData?.droneNumber || '',
      policyNumber: r.questionnaireData?.policyNumber || '',
      insuredName: r.questionnaireData?.insuredName || '',
      insuredAddress: r.questionnaireData?.insuredStreetAddress || '',
      insuredState: r.questionnaireData?.insuredState || '',
      insuredZipCode: r.questionnaireData?.insuredZipCode || '',
      dateOfInspection: r.questionnaireData?.dateOfInspection || '',
    }));
    const csv = Papa.unparse(flat);
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'reports.csv';
    a.click();
    URL.revokeObjectURL(url);
  }

  function exportPDF() {
    const pdf = new jsPDF({ orientation: 'p', unit: 'pt', format: 'a4' });
    let y = 40;
    pdf.setFontSize(16);
    pdf.text('Inspection Reports', 40, y);
    y += 30;
    pdf.setFontSize(10);
    filtered.forEach((r, i) => {
      pdf.text(`ID: ${r.id}`, 40, y);
      pdf.text(`Report: ${r.reportName}`, 150, y);
      pdf.text(`User: ${r.userName}`, 320, y);
      pdf.text(`Status: ${r.status}`, 450, y);
      y += 16;

      // Add property information
      if (r.property) {
        pdf.text(`Property: ${r.property}`, 60, y);
        y += 14;
      }

      // Add questionnaire data if available
      if (r.questionnaireData) {
        const qData = r.questionnaireData;
        if (qData.inspectorName) {
          pdf.text(`Inspector: ${qData.inspectorName}`, 60, y);
          y += 14;
        }
        if (qData.policyNumber) {
          pdf.text(`Policy: ${qData.policyNumber}`, 60, y);
          y += 14;
        }
        if (qData.insuredName) {
          pdf.text(`Insured: ${qData.insuredName}`, 60, y);
          y += 14;
        }
      }

      y += 10;
      if (y > 750 && i < filtered.length - 1) {
        pdf.addPage();
        y = 40;
      }
    });
    pdf.save('reports.pdf');
  }

  return (
    <div className="space-y-8">
      <div>
        <div className="text-lg font-semibold mb-2">Export Reports</div>
        <div className="flex flex-wrap gap-4 items-end bg-gray-50 p-4 rounded-lg border">
          <div>
            <label className="block text-xs font-medium mb-1">User Name</label>
            <select className="border rounded px-3 py-2 w-40 focus:ring-2 focus:ring-blue-200 transition" value={userName} onChange={e => setUserName(e.target.value)}>
              <option value="">All</option>
              {inspectors.map(i => <option key={i} value={i}>{i}</option>)}
            </select>
          </div>
          <div>
            <label className="block text-xs font-medium mb-1">Status</label>
            <select className="border rounded px-3 py-2 w-32 focus:ring-2 focus:ring-blue-200 transition" value={status} onChange={e => setStatus(e.target.value)}>
              <option value="">All</option>
              {statuses.map(s => <option key={s} value={s}>{s}</option>)}
            </select>
          </div>
          <div>
            <label className="block text-xs font-medium mb-1">Date From</label>
            <input type="date" className="border rounded px-3 py-2 w-36 focus:ring-2 focus:ring-blue-200 transition" value={dateFrom} onChange={e => setDateFrom(e.target.value)} />
          </div>
          <div>
            <label className="block text-xs font-medium mb-1">Date To</label>
            <input type="date" className="border rounded px-3 py-2 w-36 focus:ring-2 focus:ring-blue-200 transition" value={dateTo} onChange={e => setDateTo(e.target.value)} />
          </div>
          <div className="flex gap-2 mt-4 sm:mt-0">
            <button className="px-4 py-2 rounded bg-brand-purple text-white font-semibold hover:bg-brand-purple/80 transition flex items-center gap-1" onClick={exportCSV} disabled={filtered.length === 0}>
              <ArrowDownTrayIcon className="w-5 h-5" /> Export as CSV
            </button>
            <button className="px-4 py-2 rounded bg-brand-purple text-white font-semibold hover:bg-brand-purple/80 transition flex items-center gap-1" onClick={exportPDF} disabled={filtered.length === 0}>
              <DocumentArrowDownIcon className="w-5 h-5" /> Export as PDF
            </button>
          </div>
        </div>
      </div>

      <div>
        <div className="text-lg font-semibold mb-2">Archived Reports</div>
        <div className="overflow-x-auto">
          <table className="min-w-full text-sm border-separate border-spacing-0">
            <thead>
              <tr className="text-xs text-gray-700 uppercase tracking-wider bg-gray-100">
                <th className="py-3 px-4 font-semibold text-left border-b border-gray-200">Report Name</th>
                <th className="py-3 px-4 font-semibold text-left border-b border-gray-200">User Name</th>
                <th className="py-3 px-4 font-semibold text-center border-b border-gray-200">Date</th>
              </tr>
            </thead>
            <tbody>
              {archivedReports.map((report) => (
                <tr key={report.id} className="hover:bg-blue-50 transition">
                  <td className="py-3 px-4 text-gray-700 text-left">{report.reportName}</td>
                  <td className="py-3 px-4 text-gray-700 text-left">{report.userName}</td>
                  <td className="py-3 px-4 text-gray-700 text-center">{report.date.split(' ')[0]}</td>
                </tr>
              ))}
              {archivedReports.length === 0 && (
                <tr>
                  <td colSpan={3} className="text-center py-8 text-gray-400">No archived reports found.</td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
} 