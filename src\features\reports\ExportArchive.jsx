import React, { useState } from 'react';
import <PERSON> from 'papaparse';
import jsPDF from 'jspdf';
import { ArrowDownTrayIcon, DocumentArrowDownIcon, ArrowUturnLeftIcon, EyeIcon } from '@heroicons/react/24/outline';
import { useReports } from '../../hooks/useReports';
import ReportDetails from './ReportDetails';

export default function ExportArchive() {
  const { reports, loading, error, unarchiveReportById } = useReports();
  const [userName, setUserName] = useState('');
  const [status, setStatus] = useState('');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');
  const [selectedReport, setSelectedReport] = useState(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [statusMessage, setStatusMessage] = useState('');

  // Handle loading and error states
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading reports...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-red-800 mb-2">Error Loading Reports</h3>
        <p className="text-red-600 mb-4">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
        >
          Retry
        </button>
      </div>
    );
  }

  const inspectors = Array.from(new Set(reports.map(r => r.userName).filter(Boolean)));
  const statuses = Array.from(new Set(reports.map(r => r.status).filter(Boolean)));
  const archivedReports = reports.filter(r => r.status === 'Archived');

  const filtered = reports.filter(r => {
    // Safely extract date
    let date;
    try {
      if (r.date) {
        if (r.date.includes('T')) {
          date = r.date.split('T')[0];
        } else if (r.date.includes(' ')) {
          date = r.date.split(' ')[0];
        } else {
          date = r.date;
        }
      } else {
        date = new Date().toISOString().split('T')[0];
      }
    } catch (error) {
      console.warn('Date parsing error for report:', r.id, error);
      date = new Date().toISOString().split('T')[0];
    }

    const afterFrom = !dateFrom || date >= dateFrom;
    const beforeTo = !dateTo || date <= dateTo;
    const matchUser = !userName || r.userName === userName;
    const matchStatus = !status || r.status === status;
    return afterFrom && beforeTo && matchUser && matchStatus;
  });

  const showStatus = (message, isError = false) => {
    setStatusMessage({ message, isError });
    setTimeout(() => setStatusMessage(''), 3000);
  };

  const handleUnarchive = async (reportId) => {
    if (window.confirm('Are you sure you want to unarchive this report? It will be moved back to active reports.')) {
      const result = await unarchiveReportById(reportId);
      showStatus(result.message, !result.success);
    }
  };

  const handleViewReport = (report) => {
    setSelectedReport(report);
    setModalOpen(true);
  };

  function exportCSV() {
    const flat = filtered.map(r => {
      // Extract questionnaire data safely
      const qData = r.questionnaire_responses || r.questionnaireData || {};
      
      return {
        id: r.id,
        reportName: r.reportName || '',
        userName: r.userName || '',
        inspectorName: r.inspectorName || '',
        inspectorId: r.inspectorId || '',
        property: r.property || '',
        date: r.date || '',
        status: r.status || '',
        droneNumber: qData.droneNumber || '',
        policyNumber: qData.policyNumber || '',
        insuredName: qData.insuredName || '',
        insuredAddress: qData.insuredStreetAddress || '',
        insuredState: qData.insuredState || '',
        insuredZipCode: qData.insuredZipCode || '',
        dateOfInspection: qData.dateOfInspection || '',
        archivedAt: r.archivedAt || '',
      };
    });
    
    const csv = Papa.unparse(flat);
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `reports-export-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  }

  function exportPDF() {
    const pdf = new jsPDF({ orientation: 'p', unit: 'pt', format: 'a4' });
    let y = 40;
    
    // Header
    pdf.setFontSize(16);
    pdf.text('Inspection Reports Export', 40, y);
    y += 20;
    pdf.setFontSize(10);
    pdf.text(`Generated on: ${new Date().toLocaleDateString()}`, 40, y);
    pdf.text(`Total Reports: ${filtered.length}`, 300, y);
    y += 30;

    filtered.forEach((r, i) => {
      // Check if we need a new page
      if (y > 750) {
        pdf.addPage();
        y = 40;
      }

      pdf.setFontSize(12);
      pdf.text(`Report #${i + 1}`, 40, y);
      y += 16;

      pdf.setFontSize(10);
      pdf.text(`ID: ${r.id}`, 50, y);
      y += 12;
      pdf.text(`Report Name: ${r.reportName || 'N/A'}`, 50, y);
      y += 12;
      pdf.text(`Inspector: ${r.inspectorName || r.userName || 'N/A'}`, 50, y);
      y += 12;
      pdf.text(`Inspector ID: ${r.inspectorId || 'N/A'}`, 50, y);
      y += 12;
      pdf.text(`Status: ${r.status || 'N/A'}`, 50, y);
      y += 12;
      pdf.text(`Date: ${r.date ? r.date.split('T')[0] : 'N/A'}`, 50, y);
      y += 12;

      // Add property information
      if (r.property) {
        pdf.text(`Property: ${r.property}`, 50, y);
        y += 12;
      }

      // Add questionnaire data if available
      const qData = r.questionnaire_responses || r.questionnaireData || {};
      if (Object.keys(qData).length > 0) {
        if (qData.policyNumber) {
          pdf.text(`Policy Number: ${qData.policyNumber}`, 50, y);
          y += 12;
        }
        if (qData.insuredName) {
          pdf.text(`Insured Name: ${qData.insuredName}`, 50, y);
          y += 12;
        }
        if (qData.insuredStreetAddress) {
          pdf.text(`Address: ${qData.insuredStreetAddress}`, 50, y);
          y += 12;
        }
      }

      // Add archived date if available
      if (r.archivedAt) {
        pdf.text(`Archived: ${new Date(r.archivedAt).toLocaleDateString()}`, 50, y);
        y += 12;
      }

      y += 15; // Space between reports
    });

    pdf.save(`reports-export-${new Date().toISOString().split('T')[0]}.pdf`);
  }

  return (
    <div className="space-y-8">
      <div>
        <div className="text-lg font-semibold mb-2">Export Reports</div>
        <div className="flex flex-wrap gap-4 items-end bg-gray-50 p-4 rounded-lg border">
          <div>
            <label className="block text-xs font-medium mb-1">User Name</label>
            <select className="border rounded px-3 py-2 w-40 focus:ring-2 focus:ring-blue-200 transition" value={userName} onChange={e => setUserName(e.target.value)}>
              <option value="">All</option>
              {inspectors.map(i => <option key={i} value={i}>{i}</option>)}
            </select>
          </div>
          <div>
            <label className="block text-xs font-medium mb-1">Status</label>
            <select className="border rounded px-3 py-2 w-32 focus:ring-2 focus:ring-blue-200 transition" value={status} onChange={e => setStatus(e.target.value)}>
              <option value="">All</option>
              {statuses.map(s => <option key={s} value={s}>{s}</option>)}
            </select>
          </div>
          <div>
            <label className="block text-xs font-medium mb-1">Date From</label>
            <input type="date" className="border rounded px-3 py-2 w-36 focus:ring-2 focus:ring-blue-200 transition" value={dateFrom} onChange={e => setDateFrom(e.target.value)} />
          </div>
          <div>
            <label className="block text-xs font-medium mb-1">Date To</label>
            <input type="date" className="border rounded px-3 py-2 w-36 focus:ring-2 focus:ring-blue-200 transition" value={dateTo} onChange={e => setDateTo(e.target.value)} />
          </div>
          <div className="flex gap-2 mt-4 sm:mt-0">
            <button className="px-4 py-2 rounded bg-brand-purple text-white font-semibold hover:bg-brand-purple/80 transition flex items-center gap-1" onClick={exportCSV} disabled={filtered.length === 0}>
              <ArrowDownTrayIcon className="w-5 h-5" /> Export as CSV
            </button>
            <button className="px-4 py-2 rounded bg-brand-purple text-white font-semibold hover:bg-brand-purple/80 transition flex items-center gap-1" onClick={exportPDF} disabled={filtered.length === 0}>
              <DocumentArrowDownIcon className="w-5 h-5" /> Export as PDF
            </button>
          </div>
        </div>
      </div>

      <div>
        <div className="flex justify-between items-center mb-4">
          <div className="text-lg font-semibold">Archived Reports ({archivedReports.length})</div>
          <div className="text-sm text-gray-600">
            Click on a report to view details or use actions to manage archived reports
          </div>
        </div>
        
        {/* Status Message */}
        {statusMessage && (
          <div className={`mb-4 p-4 rounded-lg ${statusMessage.isError ? 'bg-red-50 border border-red-200' : 'bg-green-50 border border-green-200'}`}>
            <p className={`font-semibold ${statusMessage.isError ? 'text-red-800' : 'text-green-800'}`}>
              {statusMessage.message}
            </p>
          </div>
        )}

        <div className="overflow-x-auto bg-white rounded-lg shadow-md border border-gray-200">
          <table className="min-w-full text-sm">
            <thead className="bg-gray-50">
              <tr className="text-xs text-gray-500 uppercase tracking-wider">
                <th className="py-3 px-4 font-semibold text-left border-b border-gray-200">Report Name</th>
                <th className="py-3 px-4 font-semibold text-left border-b border-gray-200">Inspector</th>
                <th className="py-3 px-4 font-semibold text-center border-b border-gray-200">Date</th>
                <th className="py-3 px-4 font-semibold text-center border-b border-gray-200">Archived</th>
                <th className="py-3 px-4 font-semibold text-center border-b border-gray-200">Actions</th>
              </tr>
            </thead>
            <tbody>
              {archivedReports.map((report) => (
                <tr key={report.id} className="hover:bg-blue-50 transition border-b border-gray-200">
                  <td className="py-3 px-4 text-gray-700 text-left">
                    <div className="font-medium">{report.reportName}</div>
                    <div className="text-xs text-gray-500">ID: {report.id.substring(0, 8)}...</div>
                  </td>
                  <td className="py-3 px-4 text-gray-700 text-left">
                    {report.inspectorName ? (
                      <>
                        <div className="font-medium">{report.inspectorName}</div>
                        <div className="text-xs text-gray-500">ID: {report.inspectorId}</div>
                      </>
                    ) : (
                      <div className="text-gray-600">{report.inspectorId || report.userName}</div>
                    )}
                  </td>
                  <td className="py-3 px-4 text-gray-700 text-center">
                    {(() => {
                      try {
                        if (report.date) {
                          if (report.date.includes('T')) {
                            return report.date.split('T')[0];
                          } else if (report.date.includes(' ')) {
                            return report.date.split(' ')[0];
                          } else {
                            return report.date;
                          }
                        }
                        return 'No date';
                      } catch (error) {
                        return 'Invalid date';
                      }
                    })()}
                  </td>
                  <td className="py-3 px-4 text-gray-700 text-center">
                    {report.archivedAt ? new Date(report.archivedAt).toLocaleDateString() : 'Unknown'}
                  </td>
                  <td className="py-3 px-4 text-center">
                    <div className="flex items-center justify-center gap-2">
                      <button
                        onClick={() => handleViewReport(report)}
                        className="inline-flex items-center justify-center rounded bg-blue-600 text-white hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 w-8 h-8 p-1 transition"
                        title="View Report"
                      >
                        <EyeIcon className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleUnarchive(report.id)}
                        className="inline-flex items-center justify-center rounded bg-green-600 text-white hover:bg-green-700 focus:ring-2 focus:ring-green-500 w-8 h-8 p-1 transition"
                        title="Unarchive Report"
                      >
                        <ArrowUturnLeftIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
              {archivedReports.length === 0 && (
                <tr>
                  <td colSpan={5} className="text-center py-8 text-gray-400">
                    <div className="flex flex-col items-center">
                      <div className="text-4xl mb-2">📁</div>
                      <div>No archived reports found.</div>
                      <div className="text-xs mt-1">Reports that are archived will appear here.</div>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Report Details Modal */}
      <ReportDetails
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        report={selectedReport}
      />
    </div>
  );
} 