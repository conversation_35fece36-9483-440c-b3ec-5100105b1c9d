import React, { useState, useEffect } from 'react';
import { useSettings } from '../../hooks/useSettings';
import { sendReportByEmail, testEmailConfiguration } from '../../services/emailService';
import { uploadReportToCloud, testCloudConfiguration } from '../../services/cloudStorageService';
import { getReportsFromFirestore } from '../../services/reportsService';
import SettingsTestPanel from '../../components/SettingsTestPanel';

const Toggle = ({ checked, onChange, disabled = false }) => (
  <button
    className={`w-14 h-8 flex items-center rounded-full p-1 transition-colors duration-300 focus:outline-none ${
      checked ? 'bg-brand-purple' : 'bg-gray-300'
    } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
    onClick={() => !disabled && onChange(!checked)}
    disabled={disabled}
  >
    <span
      className={`bg-white w-6 h-6 rounded-full shadow-md transform transition-transform duration-300 ${
        checked ? 'translate-x-6' : ''
      }`}
    />
  </button>
);

const LoadingSpinner = () => (
  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-brand-purple"></div>
);

export default function SettingsPage({ theme, setTheme }) {
  const {
    settings,
    loading,
    saving,
    error,
    updateProfile,
    updatePassword,
    updateDelivery,
    updateIntegration,
    updateAppearance,
    updateNotifications
  } = useSettings();

  const [localProfile, setLocalProfile] = useState({
    name: '',
    email: '',
    phone: '',
    company: ''
  });
  const [password, setPassword] = useState({
    current: '',
    new: '',
    confirm: '',
  });
  const [status, setStatus] = useState('');
  const [actionLoading, setActionLoading] = useState({
    email: false,
    cloud: false,
    testEmail: false,
    testCloud: false
  });

  // Update local state when settings are loaded
  useEffect(() => {
    if (settings) {
      setLocalProfile(settings.profile);
    }
  }, [settings]);

  // Show status message temporarily
  const showStatus = (message, isError = false) => {
    setStatus({ message, isError });
    setTimeout(() => setStatus(''), 3000);
  };

  const handleProfileChange = (e) => {
    setLocalProfile({ ...localProfile, [e.target.name]: e.target.value });
  };

  const handlePasswordInputChange = (e) => {
    setPassword({ ...password, [e.target.name]: e.target.value });
  };

  const handleThemeChange = async () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    await handleAppearanceUpdate({ theme: newTheme });
  };

  // Handle profile form submission
  const handleProfileSubmit = async (e) => {
    e.preventDefault();
    const result = await updateProfile(localProfile);
    showStatus(result.message, !result.success);
  };

  // Handle password change form submission
  const handlePasswordSubmit = async (e) => {
    e.preventDefault();

    if (password.new !== password.confirm) {
      showStatus('New passwords do not match', true);
      return;
    }

    if (password.new.length < 6) {
      showStatus('Password must be at least 6 characters', true);
      return;
    }

    const result = await updatePassword(password.current, password.new);
    if (result.success) {
      setPassword({ current: '', new: '', confirm: '' });
    }
    showStatus(result.message, !result.success);
  };

  // Handle delivery settings update
  const handleDeliveryUpdate = async (newDeliverySettings) => {
    const result = await updateDelivery(newDeliverySettings);
    showStatus(result.message, !result.success);
  };

  // Handle notification settings update
  const handleNotificationUpdate = async (newNotificationSettings) => {
    const result = await updateNotifications(newNotificationSettings);
    showStatus(result.message, !result.success);
  };

  // Handle appearance settings update
  const handleAppearanceUpdate = async (newAppearanceSettings) => {
    const result = await updateAppearance(newAppearanceSettings);
    showStatus(result.message, !result.success);
  };

  // Send test email
  const handleSendTestEmail = async () => {
    setActionLoading(prev => ({ ...prev, testEmail: true }));
    try {
      const result = await testEmailConfiguration();
      showStatus(result.message, !result.success);
    } catch (error) {
      showStatus('Failed to send test email', true);
    } finally {
      setActionLoading(prev => ({ ...prev, testEmail: false }));
    }
  };

  // Test cloud configuration
  const handleTestCloud = async () => {
    setActionLoading(prev => ({ ...prev, testCloud: true }));
    try {
      const cloudProvider = settings?.delivery?.cloudProvider || 'Google Drive';
      const result = await testCloudConfiguration(cloudProvider);
      showStatus(result.message, !result.success);
    } catch (error) {
      showStatus('Failed to test cloud configuration', true);
    } finally {
      setActionLoading(prev => ({ ...prev, testCloud: false }));
    }
  };

  // Send latest report via email
  const handleSendReportByEmail = async () => {
    setActionLoading(prev => ({ ...prev, email: true }));
    try {
      const reportsResult = await getReportsFromFirestore();
      if (!reportsResult.success || reportsResult.data.length === 0) {
        showStatus('No reports available to send', true);
        return;
      }

      const latestReport = reportsResult.data[0]; // Get the most recent report
      const result = await sendReportByEmail(latestReport);
      showStatus(result.message, !result.success);
    } catch (error) {
      showStatus('Failed to send report by email', true);
    } finally {
      setActionLoading(prev => ({ ...prev, email: false }));
    }
  };

  // Upload latest report to cloud
  const handleUploadToCloud = async () => {
    setActionLoading(prev => ({ ...prev, cloud: true }));
    try {
      const reportsResult = await getReportsFromFirestore();
      if (!reportsResult.success || reportsResult.data.length === 0) {
        showStatus('No reports available to upload', true);
        return;
      }

      const latestReport = reportsResult.data[0]; // Get the most recent report
      const cloudProvider = settings?.delivery?.cloudProvider || 'Google Drive';
      const result = await uploadReportToCloud(latestReport, cloudProvider);
      showStatus(result.message, !result.success);
    } catch (error) {
      showStatus('Failed to upload to cloud', true);
    } finally {
      setActionLoading(prev => ({ ...prev, cloud: false }));
    }
  };

  // Show loading state while settings are loading
  if (loading) {
    return (
      <div className="max-w-4xl mx-auto py-10 px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <LoadingSpinner />
            <p className="text-gray-600 mt-4">Loading settings...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state if settings failed to load
  if (error) {
    return (
      <div className="max-w-4xl mx-auto py-10 px-4 sm:px-6 lg:px-8">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-red-800 mb-2">Error Loading Settings</h3>
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto py-10 px-4 sm:px-6 lg:px-8">
      <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-8">Settings</h1>

      <div className="space-y-12">
        {/* Profile Section */}
        <form onSubmit={handleProfileSubmit} className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">User Profile</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Full Name</label>
              <input type="text" name="name" id="name" value={localProfile.name} onChange={handleProfileChange} className="mt-1 block w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-gray-900 dark:text-gray-200 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"/>
            </div>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Email Address</label>
              <input type="email" name="email" id="email" value={localProfile.email} onChange={handleProfileChange} disabled className="mt-1 block w-full px-3 py-2 bg-gray-100 dark:bg-gray-600 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-gray-500 dark:text-gray-400 cursor-not-allowed"/>
              <p className="text-xs text-gray-500 mt-1">Email cannot be changed here</p>
            </div>
            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Phone Number</label>
              <input type="tel" name="phone" id="phone" value={localProfile.phone} onChange={handleProfileChange} className="mt-1 block w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-gray-900 dark:text-gray-200 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"/>
            </div>
            <div>
              <label htmlFor="company" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Company</label>
              <input type="text" name="company" id="company" value={localProfile.company} onChange={handleProfileChange} className="mt-1 block w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-gray-900 dark:text-gray-200 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"/>
            </div>
          </div>
          <div className="mt-6">
            <button type="submit" disabled={saving} className="bg-brand-purple text-white px-4 py-2 rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center">
              {saving ? <LoadingSpinner /> : null}
              <span className={saving ? 'ml-2' : ''}>Update Profile</span>
            </button>
          </div>
        </form>

        {/* Password Section */}
        <form onSubmit={handlePasswordSubmit} className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">Change Password</h2>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
            <div>
              <label htmlFor="current" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Current Password</label>
              <input type="password" name="current" id="current" value={password.current} onChange={handlePasswordInputChange} className="mt-1 block w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-gray-900 dark:text-gray-200 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"/>
            </div>
            <div>
              <label htmlFor="new" className="block text-sm font-medium text-gray-700 dark:text-gray-300">New Password</label>
              <input type="password" name="new" id="new" value={password.new} onChange={handlePasswordInputChange} className="mt-1 block w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-gray-900 dark:text-gray-200 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"/>
            </div>
            <div>
              <label htmlFor="confirm" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Confirm New Password</label>
              <input type="password" name="confirm" id="confirm" value={password.confirm} onChange={handlePasswordInputChange} className="mt-1 block w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-gray-900 dark:text-gray-200 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"/>
            </div>
          </div>
          <div className="mt-6">
            <button type="submit" disabled={saving} className="bg-brand-purple text-white px-4 py-2 rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center">
              {saving ? <LoadingSpinner /> : null}
              <span className={saving ? 'ml-2' : ''}>Change Password</span>
            </button>
          </div>
        </form>
        
        {/* Appearance Section */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">Appearance</h2>
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-md font-medium text-gray-700 dark:text-gray-300">Dark Mode</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">Switch between light and dark themes.</p>
            </div>
            <Toggle checked={theme === 'dark'} onChange={handleThemeChange} />
          </div>
        </div>
        
        {/* Report Delivery Section */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">Email & Cloud Delivery Settings</h2>
          <div className="mb-4">
            <div className="flex flex-col sm:flex-row gap-4 mb-4">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Default Email Address</label>
                <input
                  type="email"
                  className="block w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-gray-900 dark:text-gray-200 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  value={settings?.delivery?.defaultEmail || ''}
                  onChange={e => handleDeliveryUpdate({ defaultEmail: e.target.value })}
                />
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-6 mb-4">
              <div className="flex items-center gap-2">
                <Toggle
                  checked={settings?.delivery?.sendToEmail || false}
                  onChange={v => handleDeliveryUpdate({ sendToEmail: v })}
                />
                <span className="text-gray-800 dark:text-gray-100">Send to Email</span>
              </div>
              <div className="flex items-center gap-2">
                <Toggle
                  checked={settings?.delivery?.sendToCloud || false}
                  onChange={v => handleDeliveryUpdate({ sendToCloud: v })}
                />
                <span className="text-gray-800 dark:text-gray-100">Send to Cloud</span>
                {settings?.delivery?.sendToCloud && (
                  <select
                    className="ml-2 px-2 py-1 rounded border border-gray-300 dark:bg-gray-700 dark:text-gray-200"
                    value={settings?.delivery?.cloudProvider || 'Google Drive'}
                    onChange={e => handleDeliveryUpdate({ cloudProvider: e.target.value })}
                  >
                    <option>Google Drive</option>
                    <option>Dropbox</option>
                  </select>
                )}
              </div>
              <div className="flex items-center gap-2">
                <Toggle
                  checked={settings?.delivery?.sendToInternal || false}
                  onChange={v => handleDeliveryUpdate({ sendToInternal: v })}
                />
                <span className="text-gray-800 dark:text-gray-100">Send to Internal Folder</span>
                {settings?.delivery?.sendToInternal && (
                  <input
                    type="text"
                    className="ml-2 px-2 py-1 rounded border border-gray-300 dark:bg-gray-700 dark:text-gray-200"
                    placeholder="Internal folder path or API URL"
                    value={settings?.delivery?.internalFolderPath || ''}
                    onChange={e => handleDeliveryUpdate({ internalFolderPath: e.target.value })}
                  />
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <hr className="my-4 border-gray-400" />
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mt-4">
              <button
                type="button"
                onClick={handleSendReportByEmail}
                disabled={actionLoading.email}
                className="flex flex-col items-center justify-center bg-blue-50 border-2 border-blue-200 text-blue-800 hover:bg-blue-100 rounded-lg px-6 py-4 shadow transition disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {actionLoading.email ? <LoadingSpinner /> : <span className="text-2xl mb-1">📧</span>}
                <span className="font-semibold text-sm">Send Latest Report</span>
                <span className="text-xs text-blue-600">via Email</span>
              </button>

              <button
                type="button"
                onClick={handleUploadToCloud}
                disabled={actionLoading.cloud}
                className="flex flex-col items-center justify-center bg-green-50 border-2 border-green-200 text-green-800 hover:bg-green-100 rounded-lg px-6 py-4 shadow transition disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {actionLoading.cloud ? <LoadingSpinner /> : <span className="text-2xl mb-1">☁️</span>}
                <span className="font-semibold text-sm">Upload to Cloud</span>
                <span className="text-xs text-green-600">{settings?.delivery?.cloudProvider || 'Google Drive'}</span>
              </button>

              <button
                type="button"
                onClick={handleSendTestEmail}
                disabled={actionLoading.testEmail}
                className="flex flex-col items-center justify-center bg-purple-50 border-2 border-purple-200 text-purple-800 hover:bg-purple-100 rounded-lg px-6 py-4 shadow transition disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {actionLoading.testEmail ? <LoadingSpinner /> : <span className="text-2xl mb-1">🧪</span>}
                <span className="font-semibold text-sm">Test Email</span>
                <span className="text-xs text-purple-600">Configuration</span>
              </button>

              <button
                type="button"
                onClick={handleTestCloud}
                disabled={actionLoading.testCloud}
                className="flex flex-col items-center justify-center bg-orange-50 border-2 border-orange-200 text-orange-800 hover:bg-orange-100 rounded-lg px-6 py-4 shadow transition disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {actionLoading.testCloud ? <LoadingSpinner /> : <span className="text-2xl mb-1">🔧</span>}
                <span className="font-semibold text-sm">Test Cloud</span>
                <span className="text-xs text-orange-600">Configuration</span>
              </button>
            </div>
          </div>
        </div>

        {/* Settings Test Panel */}
        <SettingsTestPanel />

        {/* Status Message */}
        {status && (
          <div className={`p-4 rounded-lg ${status.isError ? 'bg-red-50 border border-red-200' : 'bg-green-50 border border-green-200'}`}>
            <p className={`font-semibold ${status.isError ? 'text-red-800' : 'text-green-800'}`}>
              {status.message}
            </p>
          </div>
        )}
      </div>
    </div>
  );
} 