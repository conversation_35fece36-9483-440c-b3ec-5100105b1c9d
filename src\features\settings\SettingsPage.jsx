import React, { useState } from 'react';

const Toggle = ({ checked, onChange }) => (
  <button
    className={`w-14 h-8 flex items-center rounded-full p-1 transition-colors duration-300 focus:outline-none ${
      checked ? 'bg-brand-purple' : 'bg-gray-300'
    }`}
    onClick={() => onChange(!checked)}
  >
    <span
      className={`bg-white w-6 h-6 rounded-full shadow-md transform transition-transform duration-300 ${
        checked ? 'translate-x-6' : ''
      }`}
    />
  </button>
);

export default function SettingsPage({ theme, setTheme }) {
  const [profile, setProfile] = useState({
    name: '<PERSON><PERSON> <PERSON>',
    email: '<EMAIL>',
  });
  const [password, setPassword] = useState({
    current: '',
    new: '',
    confirm: '',
  });
  const [status, setStatus] = useState('');

  // --- Report Delivery State ---
  const [delivery, setDelivery] = useState(() => {
    // Load from localStorage or use defaults
    const saved = localStorage.getItem('reportDelivery');
    return saved ? JSON.parse(saved) : {
      defaultEmail: '<EMAIL>',
      sendToEmail: true,
      sendToCloud: false,
      cloudProvider: 'Google Drive',
      sendToInternal: false,
      internalFolderPath: ''
    };
  });

  const handleProfileChange = (e) => {
    setProfile({ ...profile, [e.target.name]: e.target.value });
  };

  const handlePasswordChange = (e) => {
    setPassword({ ...password, [e.target.name]: e.target.value });
  };

  const handleThemeChange = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  // --- Report Delivery Handlers ---
  const handleDeliveryChange = (field, value) => {
    setDelivery(prev => ({ ...prev, [field]: value }));
  };

  // Save all settings (including delivery) to localStorage
  const handleSubmit = (e) => {
    e.preventDefault();
    setStatus('Settings saved successfully!');
    localStorage.setItem('reportDelivery', JSON.stringify(delivery));
    setTimeout(() => setStatus(''), 3000);
  };

  return (
    <div className="max-w-4xl mx-auto py-10 px-4 sm:px-6 lg:px-8">
      <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-8">Settings</h1>
      
      <form onSubmit={handleSubmit} className="space-y-12">
        {/* Profile Section */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">User Profile</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Full Name</label>
              <input type="text" name="name" id="name" value={profile.name} onChange={handleProfileChange} className="mt-1 block w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-gray-900 dark:text-gray-200 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"/>
            </div>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Email Address</label>
              <input type="email" name="email" id="email" value={profile.email} onChange={handleProfileChange} className="mt-1 block w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-gray-900 dark:text-gray-200 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"/>
            </div>
          </div>
        </div>

        {/* Password Section */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">Change Password</h2>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
            <div>
              <label htmlFor="current" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Current Password</label>
              <input type="password" name="current" id="current" value={password.current} onChange={handlePasswordChange} className="mt-1 block w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-gray-900 dark:text-gray-200 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"/>
            </div>
            <div>
              <label htmlFor="new" className="block text-sm font-medium text-gray-700 dark:text-gray-300">New Password</label>
              <input type="password" name="new" id="new" value={password.new} onChange={handlePasswordChange} className="mt-1 block w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-gray-900 dark:text-gray-200 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"/>
            </div>
            <div>
              <label htmlFor="confirm" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Confirm New Password</label>
              <input type="password" name="confirm" id="confirm" value={password.confirm} onChange={handlePasswordChange} className="mt-1 block w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-gray-900 dark:text-gray-200 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"/>
            </div>
          </div>
        </div>
        
        {/* Appearance Section */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">Appearance</h2>
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-md font-medium text-gray-700 dark:text-gray-300">Dark Mode</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">Switch between light and dark themes.</p>
            </div>
            <Toggle checked={theme === 'dark'} onChange={handleThemeChange} />
          </div>
        </div>
        
        {/* Report Delivery Section */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">Email & Cloud Delivery Settings</h2>
          <div className="mb-4">
            <div className="flex flex-col sm:flex-row gap-4 mb-4">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Default Email Address</label>
                <input
                  type="email"
                  className="block w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-gray-900 dark:text-gray-200 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  value={delivery.defaultEmail}
                  onChange={e => handleDeliveryChange('defaultEmail', e.target.value)}
                />
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-6 mb-4">
              <div className="flex items-center gap-2">
                <Toggle checked={delivery.sendToEmail} onChange={v => handleDeliveryChange('sendToEmail', v)} />
                <span className="text-gray-800 dark:text-gray-100">Send to Email</span>
              </div>
              <div className="flex items-center gap-2">
                <Toggle checked={delivery.sendToCloud} onChange={v => handleDeliveryChange('sendToCloud', v)} />
                <span className="text-gray-800 dark:text-gray-100">Send to Cloud</span>
                {delivery.sendToCloud && (
                  <select
                    className="ml-2 px-2 py-1 rounded border border-gray-300 dark:bg-gray-700 dark:text-gray-200"
                    value={delivery.cloudProvider}
                    onChange={e => handleDeliveryChange('cloudProvider', e.target.value)}
                  >
                    <option>Google Drive</option>
                    <option>Dropbox</option>
                  </select>
                )}
              </div>
              <div className="flex items-center gap-2">
                <Toggle checked={delivery.sendToInternal} onChange={v => handleDeliveryChange('sendToInternal', v)} />
                <span className="text-gray-800 dark:text-gray-100">Send to Internal Folder</span>
                {delivery.sendToInternal && (
                  <input
                    type="text"
                    className="ml-2 px-2 py-1 rounded border border-gray-300 dark:bg-gray-700 dark:text-gray-200"
                    placeholder="Internal folder path or API URL"
                    value={delivery.internalFolderPath}
                    onChange={e => handleDeliveryChange('internalFolderPath', e.target.value)}
                  />
                )}
              </div>
            </div>
            {/* Tiles for actions */}
            <hr className="my-4 border-gray-400" />
            <div className="flex flex-col sm:flex-row justify-center items-center gap-6 mt-4">
              <button type="button" className="flex flex-col items-center justify-center bg-brand-purple/10 border-2 border-brand-purple text-sidebar-dark hover:bg-brand-purple/20 rounded-lg px-8 py-4 shadow transition">
                <span className="font-semibold mb-1">Send to Email</span>
                <span className="text-2xl">📧</span>
              </button>
              <button type="button" className="flex flex-col items-center justify-center bg-brand-purple/10 border-2 border-brand-purple text-sidebar-dark hover:bg-brand-purple/20 rounded-lg px-8 py-4 shadow transition">
                <span className="font-semibold mb-1">Send to Cloud Folder</span>
                <span className="text-2xl">🔄</span>
              </button>
            </div>
          </div>
        </div>
        
        {/* Save Button */}
        <div className="flex justify-end items-center gap-4">
          {status && <span className="text-green-600 font-semibold">{status}</span>}
          <button type="submit" className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-brand-purple hover:bg-brand-purple/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-purple">
            Save Settings
          </button>
        </div>
      </form>
    </div>
  );
} 