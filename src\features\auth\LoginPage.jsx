import React, { useState } from 'react';
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';
import { loginUser } from '../../services/authService';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const result = await loginUser(email, password);
      if (!result.success) {
        setError(result.message);
      }
      // If successful, the AuthContext will automatically handle the state change
    } catch (error) {
      setError('An unexpected error occurred. Please try again.');
      console.error('Login error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#e3c7f7] via-white to-[#00001b] flex items-center justify-center p-4">
      <div className="flex flex-col md:flex-row bg-white/70 backdrop-blur-md border-2 border-brand-purple rounded-2xl shadow-2xl max-w-4xl w-full">
        {/* Left Panel: Welcome Message */}
        <div className="md:w-1/2 p-8 xs:p-12 flex flex-col justify-center items-center bg-brand-purple text-white rounded-t-2xl md:rounded-l-2xl md:rounded-tr-none">
          <h1 className="text-3xl xs:text-4xl font-bold mb-4 font-sans">Welcome Back!</h1>
          <p className="text-center mb-8 font-medium">
            Log in to manage your inspections and reports with ease.
          </p>
        </div>

        {/* Right Panel: Login Form */}
        <div className="md:w-1/2 p-8 xs:p-12">
          <h2 className="text-2xl xs:text-3xl font-bold text-sidebar-dark mb-6 text-center font-sans">Admin Login</h2>
          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label className="block text-sidebar-dark text-sm font-bold mb-2" htmlFor="email">
                Email Address
              </label>
              <input
                type="email"
                id="email"
                placeholder="<EMAIL>"
                className="w-full px-4 py-3 rounded-lg bg-white/80 border border-brand-purple focus:outline-none focus:ring-2 focus:ring-brand-purple text-sidebar-dark"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            <div className="mb-6 relative">
              <label className="block text-sidebar-dark text-sm font-bold mb-2" htmlFor="password">
                Password
              </label>
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                placeholder="••••••••"
                className="w-full px-4 py-3 pr-10 rounded-lg bg-white/80 border border-brand-purple focus:outline-none focus:ring-2 focus:ring-brand-purple text-sidebar-dark"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 top-7 pr-3 flex items-center text-gray-600"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeSlashIcon className="h-5 w-5" />
                ) : (
                  <EyeIcon className="h-5 w-5" />
                )}
              </button>
            </div>
            {error && (
              <p className="text-red-500 text-sm text-center mb-4">{error}</p>
            )}
            <button
              type="submit"
              disabled={loading}
              className="w-full bg-brand-purple text-white hover:bg-brand-purple/80 disabled:opacity-50 disabled:cursor-not-allowed font-bold py-3 px-4 rounded-lg shadow transition-colors"
            >
              {loading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Signing In...
                </div>
              ) : (
                'Log In'
              )}
            </button>
          </form>
          <p className="text-center text-sm text-gray-500 mt-6">
            <a href="#" className="hover:text-brand-purple">Forgot Password?</a>
          </p>
        </div>
      </div>
    </div>
  );
} 