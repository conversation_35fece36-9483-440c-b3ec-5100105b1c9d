import { doc, getDoc, setDoc, updateDoc } from "firebase/firestore";
import { db } from "../config/firebase";

// Firestore document reference for questions
const QUESTIONS_DOC_ID = "inspection-questions";
const questionsDocRef = doc(db, "questions", QUESTIONS_DOC_ID);

/**
 * Get questions data from Firestore
 * @returns {Promise<Object>} Questions data structure with questions array and sections
 */
export const getQuestionsFromFirestore = async () => {
  try {
    const docSnap = await getDoc(questionsDocRef);
    
    if (docSnap.exists()) {
      const data = docSnap.data();
      return {
        success: true,
        data: {
          questions: data.questions || [],
          sections: data.sections || []
        },
        message: "Questions loaded successfully"
      };
    } else {
      return {
        success: false,
        error: "not-found",
        message: "Questions document not found in Firestore"
      };
    }
  } catch (error) {
    console.error("Error getting questions from Firestore:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to load questions from Firestore"
    };
  }
};

/**
 * Save complete questions data to Firestore
 * @param {Array} questions - Array of question objects
 * @param {Array} sections - Array of section objects
 * @returns {Promise<Object>} Success status
 */
export const saveQuestionsToFirestore = async (questions, sections) => {
  try {
    const questionsData = {
      questions: questions,
      sections: sections,
      lastUpdated: new Date().toISOString(),
      version: "1.0"
    };

    await setDoc(questionsDocRef, questionsData);
    
    return {
      success: true,
      message: "Questions saved successfully to Firestore"
    };
  } catch (error) {
    console.error("Error saving questions to Firestore:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to save questions to Firestore"
    };
  }
};

/**
 * Update only the questions array in Firestore (preserves sections)
 * @param {Array} questions - Updated questions array
 * @returns {Promise<Object>} Success status
 */
export const updateQuestionsInFirestore = async (questions) => {
  try {
    await updateDoc(questionsDocRef, {
      questions: questions,
      lastUpdated: new Date().toISOString()
    });
    
    return {
      success: true,
      message: "Questions updated successfully in Firestore"
    };
  } catch (error) {
    console.error("Error updating questions in Firestore:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to update questions in Firestore"
    };
  }
};

/**
 * Update only the sections array in Firestore (preserves questions)
 * @param {Array} sections - Updated sections array
 * @returns {Promise<Object>} Success status
 */
export const updateSectionsInFirestore = async (sections) => {
  try {
    await updateDoc(questionsDocRef, {
      sections: sections,
      lastUpdated: new Date().toISOString()
    });
    
    return {
      success: true,
      message: "Sections updated successfully in Firestore"
    };
  } catch (error) {
    console.error("Error updating sections in Firestore:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to update sections in Firestore"
    };
  }
};




