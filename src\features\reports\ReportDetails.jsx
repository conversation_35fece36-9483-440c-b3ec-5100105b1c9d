import { Fragment, useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import AnsweredQuestionnaire from './AnsweredQuestionnaire';
import { ExclamationTriangleIcon, HomeModernIcon, ArrowPathRoundedSquareIcon, PhotoIcon, ArrowLongRightIcon, ArrowLongLeftIcon } from '@heroicons/react/24/outline';
import { useQuestions } from '../../hooks/useQuestions';

export default function ReportDetails({ open, onClose, report }) {
  const [downloading, setDownloading] = useState(false);
  const { questions, sections } = useQuestions();

  const handleDownloadPdf = async () => {
    setDownloading(true);
    const doc = new jsPDF({ orientation: 'p', unit: 'pt', format: 'a4' });
    let y = 40;
    doc.setFontSize(20);
    doc.text(`Report #${report.id}`, 40, y);
    y += 24;
    doc.setFontSize(12);
    // Insured/Instructions Info (matches modal)
    doc.text(`Insured: ${report.insuredName || '<PERSON>'}`, 40, y); y += 16;
    doc.text(`Address: ${report.insuredStreetAddress || '6759 Biltmore Ct. Mobile'}`, 40, y); y += 16;
    doc.text(`State: ${report.insuredState || 'Alabama'}`, 40, y); y += 16;
    doc.text(`Zip Code: ${report.insuredZipCode || '36693'}`, 40, y); y += 16;
    doc.text(`Policy Number: ${report.policyNumber || '2025TEST123'}`, 40, y); y += 16;
    doc.text(`Inspection Date: ${report.date ? report.date.split(' ')[0] : '5/7/2025'}`, 40, y); y += 16;
    doc.text(`Inspector's Name: ${report.userName || 'John Doe'}`, 40, y); y += 24;
    doc.setFontSize(11);
    doc.text('Dear Mercury Insurance,', 40, y); y += 14;
    doc.text(`Please see the below underwriting inspection report outlining the overview of the property at ${report.insuredStreetAddress || '6759 Biltmore Ct. Mobile'}, ${report.insuredState || 'AL'} ${report.insuredZipCode || '36693'}. The assignment was submitted to Safe Harbor with a request to complete an exterior underwriting inspection. Thank you for the opportunity to provide you with this report.`, 40, y, { maxWidth: 500 });
    y += 40;
    // Questionnaire grouped by section (matches modal)
    const questionMap = Object.fromEntries(questions.map(q => [q.id, q]));
    doc.setFontSize(16);
    doc.text('Answered Questionnaire', 40, y); y += 18;
    doc.setFontSize(12);
    sections.forEach(section => {
      doc.setFontSize(13);
      doc.setTextColor(30, 64, 175);
      doc.text(section.title, 40, y); y += 14;
      doc.setTextColor(0, 0, 0);
      section.questions.forEach(qid => {
        const q = questionMap[qid];
        if (!q) return;
        let answer = report.questionnaireData && report.questionnaireData[qid];
        if (answer === undefined || answer === null || answer === '' || (Array.isArray(answer) && answer.length === 0)) {
          answer = 'No';
        } else if (Array.isArray(answer)) {
          answer = answer.join(', ');
        } else {
          answer = String(answer);
        }
        if (q.hasOther && answer === 'Other') {
          const other = report.questionnaireData[`${qid}Other`];
          if (other) answer = `Other: ${other}`;
        }
        doc.setFontSize(11);
        doc.text(`${q.label}:`, 50, y);
        doc.text(answer, 300, y);
        y += 13;
        if (y > 750) { doc.addPage(); y = 40; }
      });
      y += 6;
    });
    // Summary
    doc.setFontSize(13);
    doc.setTextColor(30, 64, 175);
    doc.text('Summary', 40, y); y += 14;
    doc.setTextColor(0, 0, 0);
    doc.setFontSize(11);
    const summary = report.questionnaireData && report.questionnaireData.summary
      ? report.questionnaireData.summary
      : "The property is generally in good condition with no evidence of roof damage, exterior elevation damage was noted to right elevation window along with tipping hazard on front walkway. The property has been inspected, and the surrounding area is free of debris. A six-foot wooden fence enclosing the property's rear is in good condition. No additional recommendations are being made at this time.";
    doc.text(summary, 50, y, { maxWidth: 500 });
    y += 40;
    // Photos grouped by 9 sections, matching modal logic
    const photoSections = [
      {
        title: 'Primary Risk Photo',
        subtext: 'A single photo highlighting the primary risk or concern.',
        getLabel: (idx) => idx === 0 ? 'Dwelling > Exterior > Risk' : '',
      },
      {
        title: 'Front Elevation',
        subtext: 'Photos of the front elevation.',
        getLabel: () => 'Dwelling > Exterior > Front Elevation',
      },
      {
        title: 'Left Elevation',
        subtext: 'Photos of the left elevation.',
        getLabel: () => 'Dwelling > Exterior > Left Elevation',
      },
      {
        title: 'Rear Elevation',
        subtext: 'Photos of the rear elevation.',
        getLabel: () => 'Dwelling > Exterior > Rear Elevation',
      },
      {
        title: 'Right Elevation',
        subtext: 'Photos of the right elevation.',
        getLabel: () => 'Dwelling > Exterior > Right Elevation',
      },
      {
        title: 'Roof',
        subtext: 'Photos of the roof.',
        getLabel: () => 'Dwelling > Exterior > Roof',
      },
      {
        title: 'Additional Photos',
        subtext: 'Additional photos of the property.',
        getLabel: () => 'Dwelling > Exterior > Additional Photos',
      },
      {
        title: 'Additional Photos',
        subtext: 'Additional photos of the property.',
        getLabel: () => 'Dwelling > Exterior > Additional Photos',
      },
      {
        title: 'Additional Photos',
        subtext: 'Additional photos of the property.',
        getLabel: () => 'Dwelling > Exterior > Additional Photos',
      },
    ];
    // Distribute report.photos into 9x4 grid (modal logic)
    const sectionedPhotos = Array.from({ length: 9 }, () => Array(4).fill(null));
    if (report.photos && report.photos.length > 0) {
      let pIdx = 0;
      for (let s = 0; s < 9; s++) {
        for (let i = 0; i < 4; i++) {
          if (pIdx < report.photos.length) {
            sectionedPhotos[s][i] = report.photos[pIdx];
            pIdx++;
          }
        }
      }
    }
    doc.setFontSize(16);
    doc.text('Attached Photos:', 40, y); y += 18;
    for (let sectionIdx = 0; sectionIdx < 9; sectionIdx++) {
      const section = photoSections[sectionIdx];
      doc.setFontSize(13);
      doc.setTextColor(30, 64, 175);
      doc.text(section.title, 50, y); y += 13;
      doc.setFontSize(10);
      doc.setTextColor(100, 100, 100);
      doc.text(section.subtext, 55, y); y += 12;
      doc.setTextColor(0, 0, 0);
      let x = 50;
      for (let photoIdx = 0; photoIdx < 4; photoIdx++) {
        const photoUrl = sectionedPhotos[sectionIdx][photoIdx];
        if (photoUrl) {
          try {
            const imgData = await getImageDataUrl(photoUrl);
            doc.addImage(imgData, 'JPEG', x, y, 100, 75);
            doc.setFontSize(8);
            doc.text(section.getLabel(photoIdx), x, y + 80);
            doc.text('Date Taken: ' + (report.date ? report.date.split(' ')[0] : ''), x, y + 90);
          } catch (e) {
            doc.setFontSize(8);
            doc.text('Image failed to load', x, y + 40);
          }
        } else {
          doc.setFontSize(8);
          doc.text('No photo', x, y + 40);
        }
        x += 120;
      }
      y += 100;
      if (y > 700 && sectionIdx < 8) { doc.addPage(); y = 40; }
    }
    doc.save(`report-${report.id}.pdf`);
    setDownloading(false);
  };

  // Helper to fetch image as data URL
  async function getImageDataUrl(url) {
    const res = await fetch(url);
    const blob = await res.blob();
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result);
      reader.readAsDataURL(blob);
    });
  }

  if (!open || !report) return null;

  return (
    <Transition.Root show={open} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-2xl bg-white/80 backdrop-blur-md text-black shadow-xl transition-all sm:my-8 w-full max-w-lg sm:max-w-2xl border-2 border-brand-purple">
                <div className="p-4">
                  {/* Photos */}
                  <div className="mb-6">
                    <h4 className="font-semibold mb-2">Attached Photos:</h4>
                    <div className="max-h-[65vh] overflow-y-auto p-2 sm:py-2 sm:pl-2 sm:pr-4 border border-gray-300 rounded-md bg-white flex flex-col gap-8">
                      {[...Array(9)].map((_, sectionIdx) => {
                        // Section headers based on sectionIdx
                        let sectionTitle = '';
                        let sectionSubtext = '';
                        let SectionIcon = null;
                        if (sectionIdx === 0) {
                          sectionTitle = 'Primary Risk Photo';
                          sectionSubtext = 'A single photo highlighting the primary risk or concern.';
                          SectionIcon = ExclamationTriangleIcon;
                        } else if (sectionIdx === 1) {
                          sectionTitle = 'Front Elevation';
                          sectionSubtext = 'Photos of the front elevation.';
                          SectionIcon = HomeModernIcon;
                        } else if (sectionIdx === 2) {
                          sectionTitle = 'Left Elevation';
                          sectionSubtext = 'Photos of the left elevation.';
                          SectionIcon = ArrowLongLeftIcon;
                        } else if (sectionIdx === 3) {
                          sectionTitle = 'Rear Elevation';
                          sectionSubtext = 'Photos of the rear elevation.';
                          SectionIcon = HomeModernIcon;
                        } else if (sectionIdx === 4) {
                          sectionTitle = 'Right Elevation';
                          sectionSubtext = 'Photos of the right elevation.';
                          SectionIcon = ArrowLongRightIcon;
                        } else if (sectionIdx === 5) {
                          sectionTitle = 'Roof';
                          sectionSubtext = 'Photos of the roof.';
                          SectionIcon = ArrowPathRoundedSquareIcon;
                        } else if (sectionIdx >= 6) {
                          sectionTitle = 'Additional Photos';
                          sectionSubtext = 'Additional photos of the property.';
                          SectionIcon = PhotoIcon;
                        }
                        return (
                          <div key={sectionIdx} className="border border-gray-300 rounded-lg p-4 bg-gray-50">
                            <div className="flex items-center gap-2 mb-1">
                              {SectionIcon && <SectionIcon className="w-5 h-5 text-blue-500" />}
                              <span className="text-blue-700 font-semibold text-base">{sectionTitle}</span>
                            </div>
                            <div className="text-xs text-gray-500 mb-2 ml-7">{sectionSubtext}</div>
                            <div className="grid grid-cols-2 gap-4">
                              {[...Array(4)].map((_, photoIdx) => (
                                <div key={photoIdx} className="flex flex-col items-center justify-center border border-gray-300 rounded-md bg-white p-2 min-h-[120px]">
                                  <img
                                    src={"https://oddisodd.com/wp-content/uploads/2024/04/3d-rendering-individual-modern-house-1-1-1.jpg"}
                                    alt={`Section ${sectionIdx + 1} Photo ${photoIdx + 1}`}
                                    className="w-full h-24 object-cover rounded mb-2"
                                  />
                                  {sectionIdx === 0 ? (
                                    <span className="text-xs text-gray-700 text-center">
                                      {photoIdx === 0 && (<><b>Dwelling &gt; Exterior &gt; Risk</b><br/>Date Taken: 2025/06/28</>)}
                                      {photoIdx === 1 && (<><b>Dwelling &gt; Exterior &gt; Front Elevation</b><br/>Date Taken: 2025/06/28</>)}
                                      {photoIdx === 2 && (<><b>Dwelling &gt; Exterior &gt; Front Elevation</b><br/>Date Taken: 2025/06/28</>)}
                                      {photoIdx === 3 && (<><b>Dwelling &gt; Exterior &gt; Front Elevation</b><br/>Date Taken: 2025/06/28</>)}
                                    </span>
                                  ) : sectionIdx === 1 ? (
                                    <span className="text-xs text-gray-700 text-center">
                                      {photoIdx === 0 && (<><b>Dwelling &gt; Exterior &gt; Front Elevation</b><br/>Date Taken: 2025/06/28</>)}
                                      {photoIdx === 1 && (<><b>Dwelling &gt; Exterior &gt; Left Elevation</b><br/>Date Taken: 2025/06/28</>)}
                                      {photoIdx === 2 && (<><b>Dwelling &gt; Exterior &gt; Left Elevation</b><br/>Date Taken: 2025/06/28</>)}
                                      {photoIdx === 3 && (<><b>Dwelling &gt; Exterior &gt; Left Elevation</b><br/>Date Taken: 2025/06/28</>)}
                                    </span>
                                  ) : sectionIdx === 2 ? (
                                    <span className="text-xs text-gray-700 text-center">
                                      {photoIdx === 0 && (<><b>Dwelling &gt; Exterior &gt; Left Elevation</b><br/>Date Taken: 2025/06/28</>)}
                                      {photoIdx === 1 && (<><b>Dwelling &gt; Exterior &gt; Left Elevation</b><br/>Date Taken: 2025/06/28</>)}
                                      {photoIdx === 2 && (<><b>Dwelling &gt; Exterior &gt; Left Elevation</b><br/>Date Taken: 2025/06/28</>)}
                                      {photoIdx === 3 && (<><b>Dwelling &gt; Exterior &gt; Rear Elevation</b><br/>Date Taken: 2025/06/28</>)}
                                    </span>
                                  ) : sectionIdx === 3 ? (
                                    <span className="text-xs text-gray-700 text-center">
                                      {photoIdx === 0 && (<><b>Dwelling &gt; Exterior &gt; Rear Elevation</b><br/>Date Taken: 2025/06/28</>)}
                                      {photoIdx === 1 && (<><b>Dwelling &gt; Exterior &gt; Rear Elevation</b><br/>Date Taken: 2025/06/28</>)}
                                      {photoIdx === 2 && (<><b>Dwelling &gt; Exterior &gt; Rear Elevation</b><br/>Date Taken: 2025/06/28</>)}
                                      {photoIdx === 3 && (<><b>Dwelling &gt; Exterior &gt; Right Elevation</b><br/>Date Taken: 2025/06/28</>)}
                                    </span>
                                  ) : sectionIdx === 4 ? (
                                    <span className="text-xs text-gray-700 text-center">
                                      {photoIdx === 0 && (<><b>Dwelling &gt; Exterior &gt; Right Elevation</b><br/>Date Taken: 2025/06/28</>)}
                                      {photoIdx === 1 && (<><b>Dwelling &gt; Exterior &gt; Right Elevation</b><br/>Date Taken: 2025/06/28</>)}
                                      {photoIdx === 2 && (<><b>Dwelling &gt; Exterior &gt; Right Elevation</b><br/>Date Taken: 2025/06/28</>)}
                                      {photoIdx === 3 && (<><b>Dwelling &gt; Exterior &gt; Roof</b><br/>Date Taken: 2025/06/28</>)}
                                    </span>
                                  ) : sectionIdx === 5 ? (
                                    <span className="text-xs text-gray-700 text-center">
                                      {photoIdx === 0 && (<><b>Dwelling &gt; Exterior &gt; Roof</b><br/>Date Taken: 2025/06/28</>)}
                                      {photoIdx === 1 && (<><b>Dwelling &gt; Exterior &gt; Roof</b><br/>Date Taken: 2025/06/28</>)}
                                      {photoIdx === 2 && (<><b>Dwelling &gt; Exterior &gt; Roof</b><br/>Date Taken: 2025/06/28</>)}
                                      {photoIdx === 3 && (<><b>Dwelling &gt; Exterior &gt; Roof</b><br/>Date Taken: 2025/06/28</>)}
                                    </span>
                                  ) : sectionIdx === 6 ? (
                                    <span className="text-xs text-gray-700 text-center">
                                      {photoIdx === 0 && (<><b>Dwelling &gt; Exterior &gt; Additional Photos</b><br/>Date Taken: 2025/06/28</>)}
                                      {photoIdx === 1 && (<><b>Dwelling &gt; Exterior &gt; Additional Photos</b><br/>Date Taken: 2025/06/28</>)}
                                      {photoIdx === 2 && (<><b>Dwelling &gt; Exterior &gt; Additional Photos</b><br/>Date Taken: 2025/06/28</>)}
                                      {photoIdx === 3 && (<><b>Dwelling &gt; Exterior &gt; Additional Photos</b><br/>Date Taken: 2025/06/28</>)}
                                    </span>
                                  ) : sectionIdx === 7 ? (
                                    <span className="text-xs text-gray-700 text-center">
                                      {photoIdx === 0 && (<><b>Dwelling &gt; Exterior &gt; Additional Photos</b><br/>Date Taken: 2025/06/28</>)}
                                      {photoIdx === 1 && (<><b>Dwelling &gt; Exterior &gt; Additional Photos</b><br/>Date Taken: 2025/06/28</>)}
                                      {photoIdx === 2 && (<><b>Dwelling &gt; Exterior &gt; Additional Photos</b><br/>Date Taken: 2025/06/28</>)}
                                      {photoIdx === 3 && (<><b>Dwelling &gt; Exterior &gt; Additional Photos</b><br/>Date Taken: 2025/06/28</>)}
                                    </span>
                                  ) : sectionIdx === 8 ? (
                                    <span className="text-xs text-gray-700 text-center">
                                      {photoIdx === 0 && (<><b>Dwelling &gt; Exterior &gt; Additional Photos</b><br/>Date Taken: 2025/06/28</>)}
                                      {photoIdx === 1 && (<><b>Dwelling &gt; Exterior &gt; Additional Photos</b><br/>Date Taken: 2025/06/28</>)}
                                      {photoIdx === 2 && (<><b>Dwelling &gt; Exterior &gt; Additional Photos</b><br/>Date Taken: 2025/06/28</>)}
                                      {photoIdx === 3 && (<><b>Dwelling &gt; Exterior &gt; Additional Photos</b><br/>Date Taken: 2025/06/28</>)}
                                    </span>
                                  ) : (
                                    <span className="text-xs text-gray-500">Photo {photoIdx + 1}</span>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        );
                      })}
                  </div>
                  </div>

                  {/* Questionnaire */}
                  <div className="max-h-[65vh] overflow-y-auto p-2 sm:py-2 sm:pl-2 sm:pr-4 border border-gray-300 rounded-md bg-white">
                    {report.status === 'Completed' ? (
                      <AnsweredQuestionnaire report={report} questions={questions} />
                    ) : (
                      <p className="text-center text-gray-500 py-8">Questionnaire not available for this report's status.</p>
                    )}
                  </div>

                  <hr className="border-t-2 border-gray-400 my-4" />

                  {/* Download Button */}
                  <div className="text-center">
                    <button
                      type="button"
                      className="inline-flex items-center justify-center rounded-md border border-transparent bg-brand-purple px-6 py-2 text-base font-medium text-white shadow-sm hover:bg-brand-purple/80 focus:outline-none focus:ring-2 focus:ring-brand-purple disabled:opacity-60"
                      onClick={handleDownloadPdf}
                      disabled={downloading}
                    >
                      {downloading ? 'Generating PDF...' : 'Download Full PDF'}
                    </button>
                  </div>
                </div>
                <div className="absolute top-0 right-0 pt-4 pr-4">
                  <button
                    type="button"
                    className="rounded-md bg-brand-purple text-white hover:bg-brand-purple/80 focus:outline-none focus:ring-2 focus:ring-brand-purple focus:ring-offset-2"
                    onClick={onClose}
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" aria-hidden="true">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
} 