import { Fragment, useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import AnsweredQuestionnaire from './AnsweredQuestionnaire';
import { ExclamationTriangleIcon, HomeModernIcon, ArrowPathRoundedSquareIcon, PhotoIcon, ArrowLongRightIcon, ArrowLongLeftIcon } from '@heroicons/react/24/outline';
import { useQuestions } from '../../hooks/useQuestions';

export default function ReportDetails({ open, onClose, report }) {
  const [downloading, setDownloading] = useState(false);
  const { questions, sections } = useQuestions();

  const handleDownloadPdf = async () => {
    setDownloading(true);
    const doc = new jsPDF({ orientation: 'p', unit: 'pt', format: 'a4' });
    let y = 40;

    // Helper function to check if we need a new page
    const checkNewPage = (requiredSpace = 50) => {
      if (y + requiredSpace > 750) {
        doc.addPage();
        y = 40;
      }
    };

    // Extract insured information (same as app)
    const getInsuredInfo = (responses) => {
      if (!responses) return {};
      const info = {};
      
      for (const [key, response] of Object.entries(responses)) {
        if (typeof response === 'object' && response.question_text && response.value) {
          const questionText = response.question_text.toLowerCase();
          
          if ((questionText.includes('insured') && questionText.includes('name')) ||
              (questionText.includes('property') && questionText.includes('owner'))) {
            info.insuredName = response.value;
          }
          if ((questionText.includes('address') && questionText.includes('street')) ||
              (questionText.includes('property') && questionText.includes('address'))) {
            info.address = response.value;
          }
          if (questionText.includes('state') && !questionText.includes('estate')) {
            info.state = response.value;
          }
          if (questionText.includes('zip') || questionText.includes('postal')) {
            info.zipCode = response.value;
          }
          if (questionText.includes('policy') && questionText.includes('number')) {
            info.policyNumber = response.value;
          }
        }
      }
      return info;
    };

    const hasQuestionnaireData = report.questionnaire_responses || report.questionnaireData;
    const insuredInfo = getInsuredInfo(hasQuestionnaireData);

    // Header
    doc.setFontSize(20);
    doc.text('Inspection Report', 40, y);
    y += 30;
    
    // Insured Information
    doc.setFontSize(12);
    doc.text(`Insured: ${insuredInfo.insuredName || 'Not specified'}`, 40, y); y += 16;
    doc.text(`Address: ${insuredInfo.address || 'Not specified'}`, 40, y); y += 16;
    doc.text(`State: ${insuredInfo.state || 'Not specified'}`, 40, y); y += 16;
    doc.text(`Zip Code: ${insuredInfo.zipCode || 'Not specified'}`, 40, y); y += 16;
    doc.text(`Policy Number: ${insuredInfo.policyNumber || 'Not specified'}`, 40, y); y += 16;
    doc.text(`Inspection Date: ${report.date ? report.date.split('T')[0] : 'Not specified'}`, 40, y); y += 16;
    
    const inspectorText = report.inspectorName ? 
      `Inspector: ${report.inspectorName} (ID: ${report.inspectorId})` : 
      `Inspector: ${report.inspectorId || 'Not specified'}`;
    doc.text(inspectorText, 40, y); y += 24;
    
    // Introduction paragraph
    doc.setFontSize(11);
    const introText = `Dear Mercury Insurance,\n\nPlease see the below underwriting inspection report outlining the overview of the property at ${insuredInfo.address || 'Property Address Not Specified'}, ${insuredInfo.state || 'State Not Specified'} ${insuredInfo.zipCode || 'Zip Not Specified'}. The assignment was submitted to Safe Harbor with a request to complete an exterior underwriting inspection. Thank you for the opportunity to provide you with this report.`;
    doc.text(introText, 40, y, { maxWidth: 500 });
    y += 50;
    // Questionnaire Section
    checkNewPage(100);
    const questionMap = Object.fromEntries(questions.map(q => [q.id, q]));
    doc.setFontSize(16);
    doc.text('Answered Questionnaire', 40, y); y += 18;
    
    sections.forEach(section => {
      checkNewPage(50);
      doc.setFontSize(13);
      doc.setTextColor(30, 64, 175);
      doc.text(section.title, 40, y); y += 14;
      doc.setTextColor(0, 0, 0);
      
      section.questions.forEach(qid => {
        const q = questionMap[qid];
        if (!q) return;
        
        checkNewPage(20);
        
        const responses = hasQuestionnaireData || {};
        let answer = 'No';
        
        if (responses[qid] && typeof responses[qid] === 'object' && responses[qid].value !== undefined) {
          answer = responses[qid].value;
        } else if (responses[qid]) {
          answer = responses[qid];
        }
        
        if (answer && answer !== 'No') {
          if (Array.isArray(answer)) {
            answer = answer.join(', ');
          } else {
            answer = String(answer);
          }
        }
        
        doc.setFontSize(11);
        doc.text(`${q.label}:`, 50, y);
        doc.text(answer, 300, y);
        y += 13;
      });
      y += 6;
    });
    
    // Summary Section
    checkNewPage(60);
    doc.setFontSize(13);
    doc.setTextColor(30, 64, 175);
    doc.text('Summary', 40, y); y += 14;
    doc.setTextColor(0, 0, 0);
    doc.setFontSize(11);
    
    const summaryText = hasQuestionnaireData?.summary || 
      "The property is generally in good condition with no evidence of roof damage, exterior elevation damage was noted to right elevation window along with tipping hazard on front walkway. The property has been inspected, and the surrounding area is free of debris. A six-foot wooden fence enclosing the property's rear is in good condition. No additional recommendations are being made at this time.";
    doc.text(summaryText, 50, y, { maxWidth: 500 });
    y += 50;
    // Photos Section - Clean and Optimized
    checkNewPage(100);
    doc.setFontSize(16);
    doc.text('Attached Photos:', 40, y); y += 18;
    
    if (report.images && Object.keys(report.images).length > 0) {
      const categoryDisplayNames = {
        'primary_risk': 'Primary Risk Photo',
        'front_elevation': 'Front Elevation',
        'left_elevation': 'Left Elevation', 
        'rear_elevation': 'Rear Elevation',
        'right_elevation': 'Right Elevation',
        'roof': 'Roof',
        'additional': 'Additional Photos'
      };
      
      for (const [category, categoryImages] of Object.entries(report.images)) {
        const displayName = categoryDisplayNames[category] || category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
        
        // Extract image URLs
        let imageUrls = [];
        if (Array.isArray(categoryImages)) {
          imageUrls = categoryImages;
        } else if (typeof categoryImages === 'object' && categoryImages !== null) {
          imageUrls = Object.values(categoryImages).filter(url => typeof url === 'string');
        } else if (typeof categoryImages === 'string') {
          imageUrls = [categoryImages];
        }
        
        if (imageUrls.length === 0) continue;
        
        // Category header
        checkNewPage(120);
        doc.setFontSize(13);
        doc.setTextColor(30, 64, 175);
        doc.text(displayName, 50, y); y += 13;
        doc.setFontSize(10);
        doc.setTextColor(100, 100, 100);
        doc.text(`${imageUrls.length} photo${imageUrls.length !== 1 ? 's' : ''} in this category`, 55, y); y += 12;
        doc.setTextColor(0, 0, 0);
        
        // Process ALL images in this category
        let x = 50;
        let imagesInRow = 0;
        const maxImagesPerRow = 2; // Reduced for better quality
        const imageWidth = 120;
        const imageHeight = 90;
        const imageSpacing = 150;
        
        for (const imageUrl of imageUrls) {
          if (imagesInRow >= maxImagesPerRow) {
            y += imageHeight + 30;
            x = 50;
            imagesInRow = 0;
            checkNewPage(120);
          }
          
          try {
            const imgData = await getImageDataUrl(imageUrl);
            doc.addImage(imgData, 'JPEG', x, y, imageWidth, imageHeight);
            
            // Image labels
            doc.setFontSize(8);
            doc.text(`Dwelling > Exterior > ${displayName}`, x, y + imageHeight + 10);
            doc.text(`Date: ${report.date ? report.date.split('T')[0] : 'Unknown'}`, x, y + imageHeight + 20);
          } catch (e) {
            // Fallback for failed images
            doc.rect(x, y, imageWidth, imageHeight);
            doc.setFontSize(8);
            doc.text('Image Load Failed', x + 10, y + imageHeight/2);
            doc.text(`Dwelling > Exterior > ${displayName}`, x, y + imageHeight + 10);
            doc.text(`Date: ${report.date ? report.date.split('T')[0] : 'Unknown'}`, x, y + imageHeight + 20);
          }
          
          x += imageSpacing;
          imagesInRow++;
        }
        
        y += imageHeight + 50; // Space between categories
      }
    } else {
      doc.setFontSize(11);
      doc.setTextColor(100, 100, 100);
      doc.text('No photos available for this report', 50, y);
    }
    doc.save(`report-${report.id}.pdf`);
    setDownloading(false);
  };

  // Helper to fetch image as data URL
  async function getImageDataUrl(url) {
    const res = await fetch(url);
    const blob = await res.blob();
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result);
      reader.readAsDataURL(blob);
    });
  }

  if (!open || !report) return null;

  return (
    <Transition.Root show={open} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-2xl bg-white/80 backdrop-blur-md text-black shadow-xl transition-all sm:my-8 w-full max-w-lg sm:max-w-2xl border-2 border-brand-purple">
                <div className="p-4">
                  {/* Photos */}
                  <div className="mb-6">
                    <h4 className="font-semibold mb-2">Attached Photos:</h4>
                    <div className="max-h-[65vh] overflow-y-auto p-2 sm:py-2 sm:pl-2 sm:pr-4 border border-gray-300 rounded-md bg-white flex flex-col gap-8">
                      {report.images && Object.keys(report.images).length > 0 ? (
                        Object.entries(report.images).map(([category, categoryImages]) => {
                          // Map Firebase categories to display names
                          const categoryDisplayNames = {
                            'primary_risk': 'Primary Risk Photo',
                            'front_elevation': 'Front Elevation',
                            'left_elevation': 'Left Elevation', 
                            'rear_elevation': 'Rear Elevation',
                            'right_elevation': 'Right Elevation',
                            'roof': 'Roof',
                            'additional': 'Additional Photos'
                          };
                          
                          const displayName = categoryDisplayNames[category] || category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
                          
                          // Handle both array and object structures
                          let imageUrls = [];
                          if (Array.isArray(categoryImages)) {
                            imageUrls = categoryImages;
                          } else if (typeof categoryImages === 'object' && categoryImages !== null) {
                            imageUrls = Object.values(categoryImages).filter(url => typeof url === 'string');
                          } else if (typeof categoryImages === 'string') {
                            imageUrls = [categoryImages];
                          }
                          
                          if (imageUrls.length === 0) return null;
                          
                          return (
                            <div key={category} className="border border-gray-300 rounded-lg p-4 bg-gray-50">
                              <div className="flex items-center gap-2 mb-1">
                                <PhotoIcon className="w-5 h-5 text-blue-500" />
                                <span className="text-blue-700 font-semibold text-base">{displayName}</span>
                              </div>
                              <div className="text-xs text-gray-500 mb-2 ml-7">
                                {imageUrls.length} photo{imageUrls.length !== 1 ? 's' : ''} in this category
                              </div>
                              <div className="grid grid-cols-2 gap-4">
                                {imageUrls.slice(0, 4).map((imageUrl, photoIdx) => (
                                  <div key={photoIdx} className="flex flex-col items-center justify-center border border-gray-300 rounded-md bg-white p-2 min-h-[120px]">
                                    <img
                                      src={imageUrl}
                                      alt={`${displayName} Photo ${photoIdx + 1}`}
                                      className="w-full h-24 object-cover rounded mb-2"
                                      onError={(e) => {
                                        e.target.src = "https://via.placeholder.com/150x100?text=Image+Not+Found";
                                      }}
                                    />
                                    <span className="text-xs text-gray-700 text-center">
                                      <b>Dwelling &gt; Exterior &gt; {displayName}</b><br/>
                                      Date Taken: {report.date ? report.date.split('T')[0] : 'Unknown'}
                                    </span>
                                  </div>
                                ))}
                                {/* Fill remaining slots if less than 4 images */}
                                {Array.from({ length: Math.max(0, 4 - imageUrls.length) }).map((_, emptyIdx) => (
                                  <div key={`empty-${emptyIdx}`} className="flex flex-col items-center justify-center border border-gray-300 rounded-md bg-gray-100 p-2 min-h-[120px]">
                                    <span className="text-xs text-gray-400">No photo</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          );
                        })
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <PhotoIcon className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                          <p>No photos available for this report</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Questionnaire */}
                  <div className="mb-6">
                    <h4 className="font-semibold mb-2">Questionnaire Responses:</h4>
                    <div className="max-h-[65vh] overflow-y-auto p-2 sm:py-2 sm:pl-2 sm:pr-4 border border-gray-300 rounded-md bg-white">
                      <AnsweredQuestionnaire report={report} questions={questions} />
                    </div>
                  </div>

                  <hr className="border-t-2 border-gray-400 my-4" />

                  {/* Download Button */}
                  <div className="text-center">
                    <button
                      type="button"
                      className="inline-flex items-center justify-center rounded-md border border-transparent bg-brand-purple px-6 py-2 text-base font-medium text-white shadow-sm hover:bg-brand-purple/80 focus:outline-none focus:ring-2 focus:ring-brand-purple disabled:opacity-60"
                      onClick={handleDownloadPdf}
                      disabled={downloading}
                    >
                      {downloading ? 'Generating PDF...' : 'Download Full PDF'}
                    </button>
                  </div>
                </div>
                <div className="absolute top-0 right-0 pt-4 pr-4">
                  <button
                    type="button"
                    className="rounded-md bg-brand-purple text-white hover:bg-brand-purple/80 focus:outline-none focus:ring-2 focus:ring-brand-purple focus:ring-offset-2"
                    onClick={onClose}
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" aria-hidden="true">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
} 