import React, { useState } from 'react';
import { 
  PlusIcon, 
  PencilIcon, 
  TrashIcon, 
  EyeIcon,
  KeyIcon,
  UserIcon,
  CheckCircleIcon,
  XCircleIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import { useInspectors } from '../hooks/useInspectors';

const LoadingSpinner = () => (
  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-brand-purple"></div>
);

const StatusBadge = ({ status }) => {
  const statusConfig = {
    active: { color: 'bg-green-100 text-green-800', icon: CheckCircleIcon, label: 'Active' },
    inactive: { color: 'bg-yellow-100 text-yellow-800', icon: XCircleIcon, label: 'Inactive' },
    deleted: { color: 'bg-red-100 text-red-800', icon: XCircleIcon, label: 'Deleted' }
  };

  const config = statusConfig[status] || statusConfig.inactive;
  const Icon = config.icon;

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
      <Icon className="w-3 h-3 mr-1" />
      {config.label}
    </span>
  );
};

const InspectorModal = ({ isOpen, onClose, onSave, inspector, mode = 'create' }) => {
  const [formData, setFormData] = useState({
    name: inspector?.name || '',
    email: inspector?.email || '',
    password: '',
    phone: inspector?.phone || '',
    company: inspector?.company || '',
    employeeId: inspector?.employeeId || ''
  });
  const [errors, setErrors] = useState({});
  const [saving, setSaving] = useState(false);

  React.useEffect(() => {
    if (inspector && mode === 'edit') {
      setFormData({
        name: inspector.name || '',
        email: inspector.email || '',
        password: '',
        phone: inspector.phone || '',
        company: inspector.company || '',
        employeeId: inspector.employeeId || ''
      });
    } else if (mode === 'create') {
      setFormData({
        name: '',
        email: '',
        password: '',
        phone: '',
        company: '',
        employeeId: ''
      });
    }
    setErrors({});
  }, [inspector, mode, isOpen]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    if (mode === 'create' && !formData.password) {
      newErrors.password = 'Password is required';
    } else if (mode === 'create' && formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setSaving(true);
    try {
      await onSave(formData);
      onClose();
    } catch (error) {
      console.error('Error saving inspector:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4" style={{ zIndex: 9999 }}>
      <div className="bg-white border-2 border-brand-purple rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto" style={{ zIndex: 10000 }}>
        <div className="p-6">
          <h2 className="text-2xl font-bold mb-6 text-sidebar-dark flex items-center">
            <UserIcon className="w-6 h-6 mr-2" />
            {mode === 'create' ? 'Add New Inspector' : 'Edit Inspector'}
          </h2>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Full Name *
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-brand-purple ${
                    errors.name ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="Enter full name"
                />
                {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
              </div>

              {/* Email */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address *
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  disabled={mode === 'edit'}
                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-brand-purple ${
                    errors.email ? 'border-red-300' : 'border-gray-300'
                  } ${mode === 'edit' ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                  placeholder="Enter email address"
                />
                {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
                {mode === 'edit' && <p className="text-xs text-gray-500 mt-1">Email cannot be changed</p>}
              </div>

              {/* Password */}
              {mode === 'create' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Password *
                  </label>
                  <input
                    type="password"
                    name="password"
                    value={formData.password}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-brand-purple ${
                      errors.password ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="Enter password (min 6 characters)"
                  />
                  {errors.password && <p className="text-red-500 text-xs mt-1">{errors.password}</p>}
                </div>
              )}

              {/* Phone */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Phone Number
                </label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-brand-purple"
                  placeholder="Enter phone number"
                />
              </div>

              {/* Company */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Company
                </label>
                <input
                  type="text"
                  name="company"
                  value={formData.company}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-brand-purple"
                  placeholder="Enter company name"
                />
              </div>

              {/* Employee ID */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Employee ID
                </label>
                <input
                  type="text"
                  name="employeeId"
                  value={formData.employeeId}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-brand-purple"
                  placeholder="Enter employee ID"
                />
              </div>
            </div>

            <div className="flex justify-end gap-4 mt-8 pt-4 border-t">
              <button
                type="button"
                onClick={onClose}
                disabled={saving}
                className="px-6 py-2 text-gray-600 font-medium rounded-lg hover:bg-gray-100 disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={saving}
                className="px-6 py-2 bg-brand-purple text-white font-medium rounded-lg hover:bg-brand-purple/80 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {saving ? (
                  <>
                    <LoadingSpinner />
                    <span className="ml-2">Saving...</span>
                  </>
                ) : (
                  mode === 'create' ? 'Create Inspector' : 'Update Inspector'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

const PasswordResetModal = ({ isOpen, onClose, onReset, inspector }) => {
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [saving, setSaving] = useState(false);

  React.useEffect(() => {
    if (isOpen) {
      setNewPassword('');
      setConfirmPassword('');
      setError('');
    }
  }, [isOpen]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (newPassword.length < 6) {
      setError('Password must be at least 6 characters');
      return;
    }

    if (newPassword !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    setSaving(true);
    try {
      await onReset(inspector.id, newPassword);
      onClose();
    } catch (error) {
      setError('Failed to reset password');
    } finally {
      setSaving(false);
    }
  };

  if (!isOpen || !inspector) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4" style={{ zIndex: 9999 }}>
      <div className="bg-white border-2 border-brand-purple rounded-2xl shadow-2xl w-full max-w-md" style={{ zIndex: 10000 }}>
        <div className="p-6">
          <h2 className="text-xl font-bold mb-4 text-sidebar-dark flex items-center">
            <KeyIcon className="w-5 h-5 mr-2" />
            Reset Password
          </h2>
          
          <p className="text-gray-600 mb-4">
            Reset password for <strong>{inspector.name}</strong> ({inspector.email})
          </p>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                New Password
              </label>
              <input
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-brand-purple"
                placeholder="Enter new password (min 6 characters)"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Confirm Password
              </label>
              <input
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-brand-purple"
                placeholder="Confirm new password"
                required
              />
            </div>

            {error && (
              <p className="text-red-500 text-sm">{error}</p>
            )}

            <div className="flex justify-end gap-4 mt-6">
              <button
                type="button"
                onClick={onClose}
                disabled={saving}
                className="px-4 py-2 text-gray-600 font-medium rounded-lg hover:bg-gray-100 disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={saving}
                className="px-4 py-2 bg-brand-purple text-white font-medium rounded-lg hover:bg-brand-purple/80 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {saving ? (
                  <>
                    <LoadingSpinner />
                    <span className="ml-2">Resetting...</span>
                  </>
                ) : (
                  'Reset Password'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default function InspectorManagement() {
  const {
    inspectors,
    statistics,
    loading,
    error,
    saving,
    addInspector,
    updateInspectorData,
    removeInspector,
    toggleStatus,
    resetPassword,
    searchInspectors,
    filterByStatus
  } = useInspectors();

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [modalOpen, setModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('create');
  const [selectedInspector, setSelectedInspector] = useState(null);
  const [passwordResetModalOpen, setPasswordResetModalOpen] = useState(false);
  const [status, setStatus] = useState('');

  // Filter inspectors based on search and status
  const filteredInspectors = React.useMemo(() => {
    let result = inspectors;
    
    if (searchTerm) {
      result = searchInspectors(searchTerm);
    }
    
    if (statusFilter !== 'all') {
      result = filterByStatus(statusFilter);
    }
    
    return result;
  }, [inspectors, searchTerm, statusFilter, searchInspectors, filterByStatus]);

  const showStatus = (message, isError = false) => {
    setStatus({ message, isError });
    setTimeout(() => setStatus(''), 3000);
  };

  const handleAddInspector = () => {
    setSelectedInspector(null);
    setModalMode('create');
    setModalOpen(true);
  };

  const handleEditInspector = (inspector) => {
    setSelectedInspector(inspector);
    setModalMode('edit');
    setModalOpen(true);
  };

  const handleSaveInspector = async (formData) => {
    try {
      let result;
      
      if (modalMode === 'create') {
        // Show special message for inspector creation
        showStatus('Creating inspector account... Please wait, your admin session will be maintained.', false);
        result = await addInspector(formData);
        
        // Add a small delay to ensure the UI updates properly
        setTimeout(() => {
          showStatus(result.message, !result.success);
        }, 500);
      } else {
        result = await updateInspectorData(selectedInspector.id, formData);
        showStatus(result.message, !result.success);
      }
    } catch (error) {
      showStatus('Failed to save inspector', true);
    }
  };

  const handleDeleteInspector = async (inspector) => {
    if (window.confirm(`Are you sure you want to delete ${inspector.name}? This action cannot be undone.`)) {
      const result = await removeInspector(inspector.id);
      showStatus(result.message, !result.success);
    }
  };

  const handleToggleStatus = async (inspector) => {
    const newStatus = inspector.status === 'active';
    const result = await toggleStatus(inspector.id, !newStatus);
    showStatus(result.message, !result.success);
  };

  const handlePasswordReset = (inspector) => {
    setSelectedInspector(inspector);
    setPasswordResetModalOpen(true);
  };

  const handlePasswordResetSubmit = async (inspectorId, newPassword) => {
    const result = await resetPassword(inspectorId, newPassword);
    showStatus(result.message, !result.success);
    
    if (result.success && result.data) {
      // Show temporary password info
      alert(`Password reset successfully!\n\nEmail: ${result.data.email}\nTemporary Password: ${result.data.temporaryPassword}\n\nThe inspector will be required to change this password on next login.`);
    }
  };

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <LoadingSpinner />
            <p className="text-gray-600 mt-4">Loading inspectors...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-red-800 mb-2">Error Loading Inspectors</h3>
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4 sm:mb-0">
          Inspector Management
        </h2>
        <button
          onClick={handleAddInspector}
          className="bg-brand-purple text-white px-4 py-2 rounded-lg hover:bg-brand-purple/80 flex items-center"
        >
          <PlusIcon className="w-4 h-4 mr-2" />
          Add Inspector
        </button>
      </div>

      {/* Statistics */}
      {statistics && (
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{statistics.total}</div>
            <div className="text-sm text-blue-800">Total</div>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{statistics.active}</div>
            <div className="text-sm text-green-800">Active</div>
          </div>
          <div className="bg-yellow-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-yellow-600">{statistics.inactive}</div>
            <div className="text-sm text-yellow-800">Inactive</div>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">{statistics.recentlyCreated}</div>
            <div className="text-sm text-purple-800">New (7 days)</div>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-gray-600">{statistics.totalReports}</div>
            <div className="text-sm text-gray-800">Total Reports</div>
          </div>
        </div>
      )}

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search inspectors..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-purple"
          />
        </div>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-purple"
        >
          <option value="all">All Status</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
        </select>
      </div>

      {/* Inspectors Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Inspector
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Contact
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Details
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredInspectors.map((inspector) => (
              <tr key={inspector.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                      <div className="h-10 w-10 rounded-full bg-brand-purple flex items-center justify-center">
                        <span className="text-white font-medium">
                          {inspector.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">{inspector.name}</div>
                      <div className="text-sm text-gray-500">{inspector.employeeId || 'No ID'}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{inspector.email}</div>
                  <div className="text-sm text-gray-500">{inspector.phone || 'No phone'}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{inspector.company || 'No company'}</div>
                  <div className="text-sm text-gray-500">Employee ID: {inspector.employeeId || 'Not set'}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <StatusBadge status={inspector.status} />
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleEditInspector(inspector)}
                      className="text-brand-purple hover:text-brand-purple/80"
                      title="Edit Inspector"
                    >
                      <PencilIcon className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handlePasswordReset(inspector)}
                      className="text-blue-600 hover:text-blue-800"
                      title="Reset Password"
                    >
                      <KeyIcon className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleToggleStatus(inspector)}
                      className={`${
                        inspector.status === 'active' ? 'text-yellow-600 hover:text-yellow-800' : 'text-green-600 hover:text-green-800'
                      }`}
                      title={inspector.status === 'active' ? 'Deactivate' : 'Activate'}
                    >
                      {inspector.status === 'active' ? <XCircleIcon className="w-4 h-4" /> : <CheckCircleIcon className="w-4 h-4" />}
                    </button>
                    <button
                      onClick={() => handleDeleteInspector(inspector)}
                      className="text-red-600 hover:text-red-800"
                      title="Delete Inspector"
                    >
                      <TrashIcon className="w-4 h-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {filteredInspectors.length === 0 && (
          <div className="text-center py-12">
            <UserIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No inspectors found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm || statusFilter !== 'all' 
                ? 'Try adjusting your search or filter criteria.'
                : 'Get started by adding your first inspector.'
              }
            </p>
          </div>
        )}
      </div>

      {/* Status Message */}
      {status && (
        <div className={`mt-4 p-4 rounded-lg ${status.isError ? 'bg-red-50 border border-red-200' : 'bg-green-50 border border-green-200'}`}>
          <p className={`font-semibold ${status.isError ? 'text-red-800' : 'text-green-800'}`}>
            {status.message}
          </p>
        </div>
      )}

      {/* Modals */}
      <InspectorModal
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
        onSave={handleSaveInspector}
        inspector={selectedInspector}
        mode={modalMode}
      />

      <PasswordResetModal
        isOpen={passwordResetModalOpen}
        onClose={() => setPasswordResetModalOpen(false)}
        onReset={handlePasswordResetSubmit}
        inspector={selectedInspector}
      />
    </div>
  );
}