import { useState, useEffect } from 'react';
import { getQuestionsFromFirestore, updateQuestionsInFirestore } from '../services/questionsService';

/**
 * Custom hook for managing questions data with Firebase Firestore
 * @returns {Object} Questions state and management functions
 */
export const useQuestions = () => {
  const [questions, setQuestions] = useState([]);
  const [sections, setSections] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [saving, setSaving] = useState(false);

  /**
   * Load questions from Firestore
   */
  const loadQuestions = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await getQuestionsFromFirestore();
      
      if (result.success) {
        setQuestions(result.data.questions);
        setSections(result.data.sections);
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      console.error('Error loading questions:', err);
      setError(err.message || 'Failed to load questions');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Save questions to Firestore
   * @param {Array} updatedQuestions - Updated questions array
   */
  const saveQuestions = async (updatedQuestions) => {
    try {
      setSaving(true);
      setError(null);

      const result = await updateQuestionsInFirestore(updatedQuestions);
      
      if (result.success) {
        setQuestions(updatedQuestions);
        return { success: true, message: result.message };
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      console.error('Error saving questions:', err);
      const errorMessage = err.message || 'Failed to save questions';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setSaving(false);
    }
  };

  /**
   * Add a new question
   * @param {Object} newQuestion - New question object
   */
  const addQuestion = async (newQuestion) => {
    const questionWithId = {
      ...newQuestion,
      id: newQuestion.id || `question_${Date.now()}`
    };
    
    const updatedQuestions = [...questions, questionWithId];
    return await saveQuestions(updatedQuestions);
  };

  /**
   * Update an existing question
   * @param {Object} updatedQuestion - Updated question object
   */
  const updateQuestion = async (updatedQuestion) => {
    const updatedQuestions = questions.map(q => 
      q.id === updatedQuestion.id ? updatedQuestion : q
    );
    return await saveQuestions(updatedQuestions);
  };

  /**
   * Delete a question
   * @param {string} questionId - ID of question to delete
   */
  const deleteQuestion = async (questionId) => {
    const updatedQuestions = questions.filter(q => q.id !== questionId);
    return await saveQuestions(updatedQuestions);
  };

  /**
   * Reorder questions
   * @param {Array} reorderedQuestions - Questions in new order
   */
  const reorderQuestions = async (reorderedQuestions) => {
    return await saveQuestions(reorderedQuestions);
  };

  /**
   * Refresh questions from Firestore
   */
  const refreshQuestions = async () => {
    await loadQuestions();
  };

  // Load questions on hook initialization
  useEffect(() => {
    loadQuestions();
  }, []);

  return {
    // State
    questions,
    sections,
    loading,
    error,
    saving,
    
    // Actions
    addQuestion,
    updateQuestion,
    deleteQuestion,
    reorderQuestions,
    refreshQuestions,
    saveQuestions,

    // Direct state setters (for local state management during editing)
    setQuestions,
    setSections
  };
};
