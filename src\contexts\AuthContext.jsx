import React, { createContext, useContext, useEffect, useState } from 'react';
import { onAuthStateChange, getCurrentUser } from '../services/authService';
import { validateInspectorCredentials } from '../services/inspectorService';

// Create the authentication context
const AuthContext = createContext({});

/**
 * Custom hook to use the authentication context
 * @returns {Object} Authentication context value
 */
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

/**
 * Authentication Provider Component
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 */
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userRole, setUserRole] = useState(null);
  const [canAccessAdminDashboard, setCanAccessAdminDashboard] = useState(false);

  /**
   * Check if user is an inspector and validate admin access
   * @param {Object} user - Firebase user object
   */
  const checkUserRole = async (user) => {
    try {
      if (!user || !user.email) {
        setUserRole('admin'); // Default to admin for authenticated users
        setCanAccessAdminDashboard(true);
        return;
      }

      // Check if user exists in inspectors collection
      const { collection, query, where, getDocs } = await import('firebase/firestore');
      const { db } = await import('../config/firebase');
      
      const inspectorsRef = collection(db, 'inspectors');
      const q = query(inspectorsRef, where('email', '==', user.email));
      const querySnapshot = await getDocs(q);
      
      if (!querySnapshot.empty) {
        // User is an inspector - deny admin dashboard access
        const inspectorDoc = querySnapshot.docs[0];
        const inspectorData = inspectorDoc.data();
        
        if (inspectorData.status === 'active') {
          setUserRole('inspector');
          setCanAccessAdminDashboard(false);
          console.log('Inspector login detected - admin dashboard access denied');
        } else {
          // Inspector account is inactive
          setUserRole('inspector_inactive');
          setCanAccessAdminDashboard(false);
          console.log('Inactive inspector login detected - admin dashboard access denied');
        }
      } else {
        // User is not an inspector, assume admin role
        setUserRole('admin');
        setCanAccessAdminDashboard(true);
        console.log('Admin login detected - admin dashboard access granted');
      }
    } catch (error) {
      console.error('Error checking user role:', error);
      // Default to admin if there's an error checking inspector status
      setUserRole('admin');
      setCanAccessAdminDashboard(true);
    }
  };

  useEffect(() => {
    // Clean up any leftover flags and old backups on startup
    window.adminSessionRestoring = false;
    
    // Clean up old admin session backups (older than 1 minute)
    const adminBackup = localStorage.getItem('adminSessionBackup');
    if (adminBackup) {
      try {
        const backup = JSON.parse(adminBackup);
        const backupAge = Date.now() - backup.timestamp;
        if (backupAge > 60000) { // 1 minute
          console.log('🗑️ Cleaning up old admin session backup on startup');
          localStorage.removeItem('adminSessionBackup');
        }
      } catch (error) {
        console.log('🗑️ Removing invalid admin session backup');
        localStorage.removeItem('adminSessionBackup');
      }
    }
    
    // Set up authentication state listener
    const unsubscribe = onAuthStateChange(async (user) => {
      // Check if we're in the middle of admin session restoration
      if (window.adminSessionRestoring) {
        console.log('🔄 Admin session restoration in progress, ignoring auth state change...');
        return;
      }

      if (user) {
        // Clear any leftover admin session backup when user successfully logs in
        localStorage.removeItem('adminSessionBackup');
        
        setUser(user);
        setIsAuthenticated(true);
        
        // Check user role and admin access permissions
        await checkUserRole(user);
        
        // Store authentication state in localStorage for persistence
        localStorage.setItem('isAuthenticated', 'true');
      } else {
        // Only check for admin session backup if we're in the middle of inspector creation
        const adminBackup = localStorage.getItem('adminSessionBackup');
        if (adminBackup && !window.adminSessionRestoring) {
          try {
            const backup = JSON.parse(adminBackup);
            // Only restore if the backup is recent (within last 30 seconds)
            const backupAge = Date.now() - backup.timestamp;
            if (backupAge < 30000) { // 30 seconds
              console.log('🔄 Detected recent admin session backup, attempting restoration...');
              
              // Set admin session restoring flag
              window.adminSessionRestoring = true;
              
              // Restore admin session
              setTimeout(() => {
                setUser({ uid: backup.uid, email: backup.email });
                setIsAuthenticated(true);
                setUserRole('admin');
                setCanAccessAdminDashboard(true);
                localStorage.setItem('isAuthenticated', 'true');
                window.adminSessionRestoring = false;
                console.log('✅ Admin session restored from backup!');
              }, 100);
              
              return;
            } else {
              // Backup is too old, remove it
              console.log('🗑️ Removing old admin session backup');
              localStorage.removeItem('adminSessionBackup');
            }
          } catch (error) {
            console.error('Error parsing admin session backup:', error);
            localStorage.removeItem('adminSessionBackup');
          }
        }

        setUser(null);
        setIsAuthenticated(false);
        setUserRole(null);
        setCanAccessAdminDashboard(false);
        // Remove authentication state from localStorage
        localStorage.removeItem('isAuthenticated');
      }
      setLoading(false);
    });

    // Listen for force admin restore events
    const handleForceAdminRestore = (event) => {
      console.log('🚨 Force admin restore event received:', event.detail);
      const { adminUid, adminEmail } = event.detail;
      
      // Immediately restore admin session
      setUser({ uid: adminUid, email: adminEmail });
      setIsAuthenticated(true);
      setUserRole('admin');
      setCanAccessAdminDashboard(true);
      localStorage.setItem('isAuthenticated', 'true');
      
      console.log('✅ Admin session forcefully restored!');
    };

    // Add event listener for force admin restore
    window.addEventListener('forceAdminRestore', handleForceAdminRestore);

    // Check if user was previously authenticated (for initial load)
    const wasAuthenticated = localStorage.getItem('isAuthenticated') === 'true';
    const currentUser = getCurrentUser();
    
    if (wasAuthenticated && currentUser) {
      setUser(currentUser);
      setIsAuthenticated(true);
      checkUserRole(currentUser);
    }

    // Cleanup subscription on unmount
    return () => {
      unsubscribe();
      window.removeEventListener('forceAdminRestore', handleForceAdminRestore);
    };
  }, []);

  /**
   * Manual cleanup function to clear any leftover session data
   */
  const clearSessionData = () => {
    console.log('🧹 Manually clearing all session data...');
    localStorage.removeItem('adminSessionBackup');
    window.adminSessionRestoring = false;
    localStorage.removeItem('isAuthenticated');
  };

  const value = {
    user,
    isAuthenticated,
    loading,
    userRole,
    canAccessAdminDashboard,
    // Helper methods
    getUserEmail: () => user?.email || null,
    getUserDisplayName: () => user?.displayName || user?.email || 'User',
    getUserId: () => user?.uid || null,
    isAdmin: () => userRole === 'admin',
    isInspector: () => userRole === 'inspector',
    clearSessionData, // Manual cleanup function
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
