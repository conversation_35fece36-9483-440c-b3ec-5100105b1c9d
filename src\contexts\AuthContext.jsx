import React, { createContext, useContext, useEffect, useState } from 'react';
import { onAuthStateChange, getCurrentUser } from '../services/authService';

// Create the authentication context
const AuthContext = createContext({});

/**
 * Custom hook to use the authentication context
 * @returns {Object} Authentication context value
 */
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

/**
 * Authentication Provider Component
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 */
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    // Set up authentication state listener
    const unsubscribe = onAuthStateChange((user) => {
      if (user) {
        setUser(user);
        setIsAuthenticated(true);
        // Store authentication state in localStorage for persistence
        localStorage.setItem('isAuthenticated', 'true');
      } else {
        setUser(null);
        setIsAuthenticated(false);
        // Remove authentication state from localStorage
        localStorage.removeItem('isAuthenticated');
      }
      setLoading(false);
    });

    // Check if user was previously authenticated (for initial load)
    const wasAuthenticated = localStorage.getItem('isAuthenticated') === 'true';
    const currentUser = getCurrentUser();
    
    if (wasAuthenticated && currentUser) {
      setUser(currentUser);
      setIsAuthenticated(true);
    }

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, []);

  const value = {
    user,
    isAuthenticated,
    loading,
    // Helper methods
    getUserEmail: () => user?.email || null,
    getUserDisplayName: () => user?.displayName || user?.email || 'User',
    getUserId: () => user?.uid || null,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
