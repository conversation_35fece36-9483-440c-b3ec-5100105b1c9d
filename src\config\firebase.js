// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyB926QhhFDqw7MgRNS_Fx0827qMHZKfeZw",
  authDomain: "safe-harbor-db2cd.firebaseapp.com",
  projectId: "safe-harbor-db2cd",
  storageBucket: "safe-harbor-db2cd.firebasestorage.app",
  messagingSenderId: "995246260968",
  appId: "1:995246260968:web:51a2563abb7f2e0d413f5d"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app);

// Initialize Cloud Firestore and get a reference to the service
export const db = getFirestore(app);

// Initialize Cloud Storage and get a reference to the service
export const storage = getStorage(app);

// Export the app instance
export default app;
