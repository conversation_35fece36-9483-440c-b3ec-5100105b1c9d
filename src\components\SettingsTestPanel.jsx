import React, { useState } from 'react';
import { getSettingsFromFirestore } from '../services/settingsService';
import { testEmailConfiguration, sendReportByEmail } from '../services/emailService';
import { testCloudConfiguration, uploadReportToCloud } from '../services/cloudStorageService';
import { getReportsFromFirestore } from '../services/reportsService';

/**
 * Settings Test Panel Component
 * Provides comprehensive testing tools for settings functionality
 */
export default function SettingsTestPanel() {
  const [testResults, setTestResults] = useState({});
  const [loading, setLoading] = useState({});

  const runTest = async (testName, testFunction) => {
    setLoading(prev => ({ ...prev, [testName]: true }));
    try {
      const result = await testFunction();
      setTestResults(prev => ({ 
        ...prev, 
        [testName]: { 
          ...result, 
          timestamp: new Date().toISOString() 
        } 
      }));
    } catch (error) {
      setTestResults(prev => ({ 
        ...prev, 
        [testName]: { 
          success: false, 
          message: error.message,
          timestamp: new Date().toISOString()
        } 
      }));
    } finally {
      setLoading(prev => ({ ...prev, [testName]: false }));
    }
  };

  const tests = [
    {
      id: 'firebase-connection',
      name: 'Firebase Connection',
      description: 'Test Firestore settings connection',
      action: () => runTest('firebase-connection', getSettingsFromFirestore)
    },
    {
      id: 'email-config',
      name: 'Email Configuration',
      description: 'Test email settings and configuration',
      action: () => runTest('email-config', testEmailConfiguration)
    },
    {
      id: 'cloud-config',
      name: 'Cloud Storage Configuration',
      description: 'Test cloud storage settings',
      action: () => runTest('cloud-config', () => testCloudConfiguration('Google Drive'))
    },
    {
      id: 'reports-fetch',
      name: 'Reports Data',
      description: 'Test reports data fetching',
      action: () => runTest('reports-fetch', getReportsFromFirestore)
    },
    {
      id: 'email-send',
      name: 'Send Test Email',
      description: 'Send actual test email with latest report',
      action: async () => {
        const reportsResult = await getReportsFromFirestore();
        if (reportsResult.success && reportsResult.data.length > 0) {
          return runTest('email-send', () => sendReportByEmail(reportsResult.data[0]));
        } else {
          setTestResults(prev => ({ 
            ...prev, 
            'email-send': { 
              success: false, 
              message: 'No reports available for testing',
              timestamp: new Date().toISOString()
            } 
          }));
        }
      }
    },
    {
      id: 'cloud-upload',
      name: 'Upload Test File',
      description: 'Upload test file to cloud storage',
      action: async () => {
        const reportsResult = await getReportsFromFirestore();
        if (reportsResult.success && reportsResult.data.length > 0) {
          return runTest('cloud-upload', () => uploadReportToCloud(reportsResult.data[0], 'Google Drive'));
        } else {
          setTestResults(prev => ({ 
            ...prev, 
            'cloud-upload': { 
              success: false, 
              message: 'No reports available for testing',
              timestamp: new Date().toISOString()
            } 
          }));
        }
      }
    }
  ];

  const TestResult = ({ result }) => {
    if (!result) return null;

    return (
      <div className={`mt-2 p-3 rounded-lg text-sm ${
        result.success 
          ? 'bg-green-50 border border-green-200 text-green-800' 
          : 'bg-red-50 border border-red-200 text-red-800'
      }`}>
        <div className="font-semibold mb-1">
          {result.success ? '✅ Success' : '❌ Failed'}
        </div>
        <div className="mb-2">{result.message}</div>
        {result.details && (
          <details className="mt-2">
            <summary className="cursor-pointer font-medium">Details</summary>
            <pre className="mt-1 text-xs bg-gray-100 p-2 rounded overflow-auto">
              {JSON.stringify(result.details, null, 2)}
            </pre>
          </details>
        )}
        <div className="text-xs text-gray-500 mt-1">
          {new Date(result.timestamp).toLocaleString()}
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
        🧪 Settings Test Panel
      </h2>
      <p className="text-gray-600 dark:text-gray-400 mb-6">
        Use these tests to verify all settings functionality is working correctly with Firebase.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {tests.map((test) => (
          <div key={test.id} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-semibold text-gray-800">{test.name}</h3>
              <button
                onClick={test.action}
                disabled={loading[test.id]}
                className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {loading[test.id] ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Testing...
                  </>
                ) : (
                  'Run Test'
                )}
              </button>
            </div>
            <p className="text-sm text-gray-600 mb-3">{test.description}</p>
            <TestResult result={testResults[test.id]} />
          </div>
        ))}
      </div>

      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="font-semibold text-blue-800 mb-2">💡 Testing Tips</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• Run "Firebase Connection" first to ensure basic connectivity</li>
          <li>• Configure email settings before testing email functionality</li>
          <li>• Make sure you have reports in Firebase before testing send/upload</li>
          <li>• Check browser console for detailed error messages</li>
          <li>• Test functions simulate real operations for demo purposes</li>
        </ul>
      </div>
    </div>
  );
}
