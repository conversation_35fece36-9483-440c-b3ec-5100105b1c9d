import { useState, useEffect } from 'react';
import { 
  getSettingsFromFirestore, 
  saveSettingsToFirestore,
  updateUserProfile,
  changeUserPassword,
  updateDeliverySettings,
  updateIntegrationSettings
} from '../services/settingsService';
import { useAuth } from '../contexts/AuthContext';

/**
 * Custom hook for managing settings with Firebase Firestore
 * @returns {Object} Settings state and management functions
 */
export const useSettings = () => {
  const { user } = useAuth();
  const [settings, setSettings] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);

  /**
   * Load settings from Firestore
   */
  const loadSettings = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await getSettingsFromFirestore();
      
      if (result.success) {
        // Merge user data from auth context
        const settingsWithUser = {
          ...result.data,
          profile: {
            ...result.data.profile,
            name: user?.displayName || result.data.profile.name || '',
            email: user?.email || result.data.profile.email || ''
          }
        };
        setSettings(settingsWithUser);
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      console.error('Error loading settings:', err);
      setError(err.message || 'Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Save all settings to Firestore
   * @param {Object} newSettings - Settings data to save
   * @returns {Promise<Object>} Success status
   */
  const saveSettings = async (newSettings) => {
    try {
      setSaving(true);
      setError(null);

      const result = await saveSettingsToFirestore(newSettings);

      if (result.success) {
        setSettings(newSettings);
        return { success: true, message: result.message };
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      console.error('Error saving settings:', err);
      const errorMessage = err.message || 'Failed to save settings';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setSaving(false);
    }
  };

  /**
   * Update profile information
   * @param {Object} profileData - Profile data to update
   * @returns {Promise<Object>} Success status
   */
  const updateProfile = async (profileData) => {
    try {
      setSaving(true);
      setError(null);

      const result = await updateUserProfile(profileData);

      if (result.success) {
        // Update local state
        setSettings(prevSettings => ({
          ...prevSettings,
          profile: {
            ...prevSettings.profile,
            ...profileData
          }
        }));
        return { success: true, message: result.message };
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      console.error('Error updating profile:', err);
      const errorMessage = err.message || 'Failed to update profile';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setSaving(false);
    }
  };

  /**
   * Change user password
   * @param {string} currentPassword - Current password
   * @param {string} newPassword - New password
   * @returns {Promise<Object>} Success status
   */
  const updatePassword = async (currentPassword, newPassword) => {
    try {
      setSaving(true);
      setError(null);

      const result = await changeUserPassword(currentPassword, newPassword);

      if (result.success) {
        return { success: true, message: result.message };
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      console.error('Error changing password:', err);
      const errorMessage = err.message || 'Failed to change password';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setSaving(false);
    }
  };

  /**
   * Update delivery settings
   * @param {Object} deliveryData - Delivery settings to update
   * @returns {Promise<Object>} Success status
   */
  const updateDelivery = async (deliveryData) => {
    try {
      setSaving(true);
      setError(null);

      const result = await updateDeliverySettings(deliveryData);

      if (result.success) {
        // Update local state
        setSettings(prevSettings => ({
          ...prevSettings,
          delivery: {
            ...prevSettings.delivery,
            ...deliveryData
          }
        }));
        return { success: true, message: result.message };
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      console.error('Error updating delivery settings:', err);
      const errorMessage = err.message || 'Failed to update delivery settings';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setSaving(false);
    }
  };

  /**
   * Update integration settings
   * @param {string} provider - Integration provider
   * @param {Object} integrationData - Integration data to update
   * @returns {Promise<Object>} Success status
   */
  const updateIntegration = async (provider, integrationData) => {
    try {
      setSaving(true);
      setError(null);

      const result = await updateIntegrationSettings(provider, integrationData);

      if (result.success) {
        // Update local state
        setSettings(prevSettings => ({
          ...prevSettings,
          integrations: {
            ...prevSettings.integrations,
            [provider]: {
              ...prevSettings.integrations[provider],
              ...integrationData
            }
          }
        }));
        return { success: true, message: result.message };
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      console.error('Error updating integration settings:', err);
      const errorMessage = err.message || 'Failed to update integration settings';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setSaving(false);
    }
  };

  /**
   * Update appearance settings
   * @param {Object} appearanceData - Appearance settings to update
   * @returns {Promise<Object>} Success status
   */
  const updateAppearance = async (appearanceData) => {
    try {
      setSaving(true);
      setError(null);

      const updatedSettings = {
        ...settings,
        appearance: {
          ...settings.appearance,
          ...appearanceData
        }
      };

      const result = await saveSettingsToFirestore(updatedSettings);

      if (result.success) {
        setSettings(updatedSettings);
        return { success: true, message: result.message };
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      console.error('Error updating appearance settings:', err);
      const errorMessage = err.message || 'Failed to update appearance settings';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setSaving(false);
    }
  };

  /**
   * Update notification settings
   * @param {Object} notificationData - Notification settings to update
   * @returns {Promise<Object>} Success status
   */
  const updateNotifications = async (notificationData) => {
    try {
      setSaving(true);
      setError(null);

      const updatedSettings = {
        ...settings,
        notifications: {
          ...settings.notifications,
          ...notificationData
        }
      };

      const result = await saveSettingsToFirestore(updatedSettings);

      if (result.success) {
        setSettings(updatedSettings);
        return { success: true, message: result.message };
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      console.error('Error updating notification settings:', err);
      const errorMessage = err.message || 'Failed to update notification settings';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setSaving(false);
    }
  };

  /**
   * Reset settings to defaults
   * @returns {Promise<Object>} Success status
   */
  const resetSettings = async () => {
    try {
      setSaving(true);
      setError(null);

      // Load default settings
      const defaultSettings = {
        profile: {
          name: user?.displayName || '',
          email: user?.email || '',
          phone: '',
          company: ''
        },
        delivery: {
          defaultEmail: '',
          sendToEmail: true,
          sendToCloud: false,
          cloudProvider: 'Google Drive',
          sendToInternal: false,
          internalFolderPath: '',
          emailTemplate: 'default',
          cloudFolderPath: '/Inspection Reports',
          autoSend: false
        },
        notifications: {
          emailNotifications: true,
          reportComplete: true,
          systemUpdates: false,
          weeklyDigest: false
        },
        appearance: {
          theme: 'light',
          language: 'en',
          timezone: 'UTC'
        },
        integrations: {
          googleDrive: {
            enabled: false,
            accessToken: null,
            refreshToken: null,
            folderId: null
          },
          dropbox: {
            enabled: false,
            accessToken: null,
            folderId: null
          },
          email: {
            smtpHost: '',
            smtpPort: 587,
            smtpUser: '',
            smtpPassword: '',
            fromEmail: '',
            fromName: 'Inspection Reports'
          }
        }
      };

      const result = await saveSettingsToFirestore(defaultSettings);

      if (result.success) {
        setSettings(defaultSettings);
        return { success: true, message: 'Settings reset to defaults' };
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      console.error('Error resetting settings:', err);
      const errorMessage = err.message || 'Failed to reset settings';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setSaving(false);
    }
  };

  /**
   * Refresh settings from Firestore
   */
  const refreshSettings = async () => {
    await loadSettings();
  };

  // Load settings on hook initialization
  useEffect(() => {
    if (user) {
      loadSettings();
    }
  }, [user]);

  return {
    // State
    settings,
    loading,
    saving,
    error,
    
    // Actions
    saveSettings,
    updateProfile,
    updatePassword,
    updateDelivery,
    updateIntegration,
    updateAppearance,
    updateNotifications,
    resetSettings,
    refreshSettings,

    // Direct state setters (for local state management)
    setSettings,
    setError
  };
};
