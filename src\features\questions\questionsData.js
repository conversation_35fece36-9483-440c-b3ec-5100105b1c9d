export const initialQuestions = [
  // Inspector Information
  { id: 'inspectorName', label: 'Inspector\'s Name', type: 'text' },
  { id: 'droneNumber', label: 'Drone Number', type: 'radio', options: [
    'AL 1', 'AL 2', 'AL 3', 'AL 4', 'AL 5', 'AL 6', 'AL 7', 'AL 8', 'AL 9', 'AL 10',
    'AL 11', 'AL 12', 'AL 13', 'AL 14', 'AL 15', 'AL 16', 'AL 17', 'AL 18', 'AL 19', 'AL 20',
    'AL 21', 'AL 22', 'AL 23', 'AL 24', 'AL 25', 'AL 26', 'AL 27', 'AL 28', 'AL 29', 'AL 30',
    'AL 31', 'AL 32', 'AL 33', 'AL 34', 'AL 35', 'AL 36', 'AL 37', 'AL 38', 'AL 39', 'AL 40',
    'AL 41', 'AL 42', 'AL 43', 'AL 44', 'AL 45', 'AL 46', 'AL 47', 'AL 48', 'AL 49', 'AL 50',
    'AL 51', 'AL 52', 'AL 53', 'AL 54', 'AL 55',
    'TX 1', 'TX 2', 'TX 3', 'TX 4', 'TX 5', 'TX 6', 'TX 7', 'TX 8', 'TX 9', 'TX 10',
    'TX 11', 'TX 12', 'TX 13', 'TX 14', 'TX 15'
  ]},

  // Insured Information
  { id: 'policyNumber', label: 'Policy Number', type: 'text' },
  { id: 'insuredName', label: 'Insured Name', type: 'text' },
  { id: 'insuredStreetAddress', label: 'Insured Street Address', type: 'text' },
  { id: 'insuredState', label: 'Insured State', type: 'radio', options: [
    'Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado', 'Connecticut', 
    'Delaware', 'Florida', 'Georgia', 'Hawaii', 'Idaho', 'Illinois', 'Indiana', 'Iowa',
    'Kansas', 'Kentucky', 'Louisiana', 'Maine', 'Maryland', 'Massachusetts', 'Michigan',
    'Minnesota', 'Mississippi', 'Missouri', 'Montana', 'Nebraska', 'Nevada', 'New Hampshire',
    'New Jersey', 'New Mexico', 'New York', 'North Carolina', 'North Dakota', 'Ohio',
    'Oklahoma', 'Oregon', 'Pennsylvania', 'Rhode Island', 'South Carolina', 'South Dakota',
    'Tennessee', 'Texas', 'Utah', 'Vermont', 'Virginia', 'Washington', 'West Virginia',
    'Wisconsin', 'Wyoming'
  ]},
  { id: 'insuredZipCode', label: 'Insured Zip Code', type: 'text' },
  { id: 'dateOfInspection', label: 'Date of Inspection', type: 'text' },

  // Location Information
  { id: 'neighborhood', label: 'Neighborhood', type: 'radio', options: ['Suburban', 'Rural', 'City'] },
  { id: 'areaEconomy', label: 'Area Economy', type: 'radio', options: ['Stable', 'Unstable', 'Declining'] },
  { id: 'gatedCommunity', label: 'Gated Community', type: 'yesno' },
  { id: 'propertyVacant', label: 'Property Vacant', type: 'yesno' },
  { id: 'nearestBodyOfWater', label: 'Nearest Body of Water', type: 'radio', options: ['Unknown', 'Other'], hasOther: true },
  { id: 'rentalProperty', label: 'Rental Property', type: 'yesnounknown' },
  { id: 'businessOnSite', label: 'Business On Site', type: 'yesno' },
  { id: 'seasonalHome', label: 'Seasonal Home', type: 'yesnounknown' },
  { id: 'historicProperty', label: 'Historic Property', type: 'yesnounknown' },
  { id: 'nearestDwelling', label: 'Nearest Dwelling (in feet)', type: 'text' },

  // Overall Elevation Condition
  { id: 'overallElevationCondition', label: 'Overall Elevation Condition', type: 'radio', options: ['Above average', 'Average', 'Below average'] },
  { id: 'dwellingType', label: 'Dwelling Type', type: 'radio', options: ['Single Family', 'Multi Family'] },
  { id: 'yearBuilt', label: 'Year Built', type: 'radio', options: ['Unknown', 'Other'], hasOther: true },
  { id: 'typeOfFoundation', label: 'Type of Foundation', type: 'radio', options: ['Slab', 'Elevated', 'Floating'] },
  { id: 'primaryConstruction', label: 'Primary Construction', type: 'radio', options: ['Wood Frame', 'Steel', 'Masonry'] },
  { id: 'numberOfStories', label: 'Number of Stories', type: 'radio', options: ['1', '2', '3', 'Other'], hasOther: true },
  { id: 'livingArea', label: 'Living Area (SF)', type: 'radio', options: ['Unknown', 'Other'], hasOther: true },
  { id: 'lotSize', label: 'Lot Size', type: 'radio', options: ['Unknown', 'Other'], hasOther: true },
  { id: 'siding', label: 'Siding', type: 'radio', options: ['Wood', 'Vinyl', 'Masonry', 'Other'], hasOther: true },
  { id: 'hvac', label: 'HVAC', type: 'radio', options: ['Central', 'Window Unit', 'Other'], hasOther: true },
  { id: 'numberOfHVACSystems', label: 'Number of HVAC Systems', type: 'radio', options: ['1', '2', 'Other'], hasOther: true },
  { id: 'hvacSerialNumbers', label: 'HVAC Serial Number(s) (Separate by commas)', type: 'text' },
  { id: 'guttersAndDownspout', label: 'Gutters and Downspout', type: 'yesno' },
  { id: 'fuelTank', label: 'Fuel Tank', type: 'yesno' },
  { id: 'sidingDamage', label: 'Siding Damage', type: 'yesno' },
  { id: 'peelingPaint', label: 'Peeling Paint', type: 'yesno' },
  { id: 'mildewMoss', label: 'Mildew/Moss', type: 'yesno' },
  { id: 'windowDamage', label: 'Window Damage', type: 'yesno' },
  { id: 'foundationCracks', label: 'Foundation Cracks', type: 'yesno' },
  { id: 'wallCracks', label: 'Wall Cracks', type: 'yesno' },
  { id: 'chimneyDamage', label: 'Chimney Damage', type: 'radio', options: ['Yes', 'No', 'N/A'] },
  { id: 'waterDamage', label: 'Water Damage', type: 'yesno' },
  { id: 'underRenovation', label: 'Under Renovation', type: 'yesno' },
  { id: 'mainBreakerPanel', label: 'Main Breaker Panel', type: 'yesno' },
  { id: 'waterSpicketDamage', label: 'Water Spicket Damage', type: 'yesno' },
  { id: 'doorDamage', label: 'Door Damage', type: 'yesno' },

  // Overall Roof Condition
  { id: 'overallRoofCondition', label: 'Overall Roof Condition', type: 'radio', options: ['Above average', 'Average', 'Below average'] },
  { id: 'roofMaterials', label: 'Roof Materials', type: 'text' },
  { id: 'roofCovering', label: 'Roof Covering', type: 'radio', options: ['Asphalt', 'Wood', 'Metal', 'Other'], hasOther: true },
  { id: 'ageOfRoof', label: 'Age of Roof (in years)', type: 'radio', options: ['0-5', '6-10', '11-15', '16+'] },
  { id: 'shapeOfRoof', label: 'Shape of Roof', type: 'radio', options: ['Hip and Valley', 'Flat', 'Gable', 'Complex', 'Hip', 'Other'], hasOther: true },
  { id: 'treeLimbsOnRoof', label: 'Tree Limbs on Roof', type: 'yesno' },
  { id: 'debrisOnRoof', label: 'Debris On Roof', type: 'yesno' },
  { id: 'solarPanel', label: 'Solar Panel', type: 'yesno' },
  { id: 'exposedFelt', label: 'Exposed Felt', type: 'yesno' },
  { id: 'missingShinglesTiles', label: 'Missing Shingles/Tiles', type: 'yesno' },
  { id: 'priorRepairs', label: 'Prior Repairs', type: 'yesno' },
  { id: 'curlingShingles', label: 'Curling Shingles', type: 'yesno' },
  { id: 'algaeMoss', label: 'Algae/Moss', type: 'yesno' },
  { id: 'tarpOnRoof', label: 'Tarp On Roof', type: 'yesno' },
  { id: 'brokenOrCrackedTiles', label: 'Broken or Cracked Tiles', type: 'radio', options: ['Yes', 'No', 'N/A'] },
  { id: 'satelliteDish', label: 'Satellite Dish', type: 'yesno' },
  { id: 'unevenDecking', label: 'Uneven Decking', type: 'yesno' },

  // Garage/Outbuilding
  { id: 'garageOutbuildingCondition', label: 'Garage/Outbuilding Overall Condition', type: 'radio', options: ['Above average', 'Average', 'Below average', 'N/A'] },
  { id: 'garageType', label: 'Garage Type', type: 'radio', options: ['Attached', 'Detached', 'N/A'] },
  { id: 'outbuilding', label: 'Outbuilding', type: 'radio', options: ['Yes', 'No', 'N/A'] },
  { id: 'outbuildingType', label: 'Outbuilding Type', type: 'radio', options: ['Vinyl', 'Metal', 'Wood', 'N/A'] },
  { id: 'fenceDetails', label: 'Fence (Height/type/details)', type: 'text' },
  { id: 'garageCondition', label: 'Garage Condition', type: 'radio', options: ['Above average', 'Average', 'Below average', 'N/A'] },
  { id: 'carportOrAwning', label: 'Carport or Awning', type: 'yesno' },
  { id: 'carportConstruction', label: 'Carport Construction', type: 'radio', options: ['Metal', 'Wood', 'N/A'] },
  { id: 'fenceCondition', label: 'Fence Condition', type: 'radio', options: ['Above average', 'Average', 'Below average', 'N/A'] },

  // Dwelling Hazard and Possible Hazards
  { id: 'boardedDoorsWindows', label: 'Boarded Doors/Windows', type: 'yesno' },
  { id: 'overgrownVegetation', label: 'Overgrown Vegetation', type: 'yesno' },
  { id: 'abandonedVehicles', label: 'Abandoned Vehicles', type: 'yesno' },
  { id: 'missingDamagedSteps', label: 'Missing/Damaged Steps', type: 'radio', options: ['Yes', 'No', 'N/A'] },
  { id: 'missingDamageRailing', label: 'Missing/Damage Railing', type: 'radio', options: ['Yes', 'No', 'N/A'] },
  { id: 'sidingDamageHazard', label: 'Siding Damage', type: 'yesno' },
  { id: 'hurricaneShutters', label: 'Hurricane Shutters', type: 'yesno' },
  { id: 'treeBranch', label: 'Tree/Branch', type: 'yesno' },
  { id: 'chimneyThroughRoof', label: 'Chimney Through Roof', type: 'yesno' },
  { id: 'fireplacePitOutside', label: 'Fireplace/Pit Outside', type: 'yesno' },
  { id: 'securityBars', label: 'Security Bars', type: 'yesno' },
  { id: 'fasciaSoffitDamage', label: 'Fascia/Soffit Damage', type: 'yesno' },

  // Possible Hazards
  { id: 'swimmingPool', label: 'Swimming Pool', type: 'yesno' },
  { id: 'divingBoardOrSlide', label: 'Diving Board or Slide', type: 'yesno' },
  { id: 'poolFenced', label: 'Pool Fenced', type: 'radio', options: ['Yes', 'No', 'N/A'] },
  { id: 'trampoline', label: 'Trampoline', type: 'yesno' },
  { id: 'swingSet', label: 'Swing Set', type: 'yesno' },
  { id: 'basketballGoal', label: 'Basketball Goal', type: 'yesno' },
  { id: 'dog', label: 'Dog', type: 'yesno' },
  { id: 'dogType', label: 'Dog Type', type: 'radio', options: ['Small', 'Medium', 'Large', 'N/A'] },
  { id: 'dogSign', label: 'Dog Sign', type: 'yesno' },
  { id: 'skateboardOrBikeRamp', label: 'Skateboard or Bike Ramp', type: 'yesno' },
  { id: 'treeHouse', label: 'Tree House', type: 'yesno' },
  { id: 'debrisInYard', label: 'Debris in Yard', type: 'yesno' }
];

export const SECTIONS = [
  {
    title: 'Inspector Information',
    questions: ['inspectorName', 'droneNumber'],
  },
  {
    title: 'Insured Information',
    questions: [
      'policyNumber', 'insuredName', 'insuredStreetAddress', 'insuredState', 'insuredZipCode', 'dateOfInspection'
    ],
  },
  {
    title: 'Location Information',
    questions: [
      'neighborhood', 'areaEconomy', 'gatedCommunity', 'propertyVacant', 'nearestBodyOfWater', 'rentalProperty', 'businessOnSite', 'seasonalHome', 'historicProperty', 'nearestDwelling'
    ],
  },
  {
    title: 'Overall Elevation Condition',
    questions: [
      'overallElevationCondition', 'dwellingType', 'yearBuilt', 'typeOfFoundation', 'primaryConstruction', 'numberOfStories', 'livingArea', 'lotSize', 'siding', 'hvac', 'numberOfHVACSystems', 'hvacSerialNumbers', 'guttersAndDownspout', 'fuelTank', 'sidingDamage', 'peelingPaint', 'mildewMoss', 'windowDamage', 'foundationCracks', 'wallCracks', 'chimneyDamage', 'waterDamage', 'underRenovation', 'mainBreakerPanel', 'waterSpicketDamage', 'doorDamage'
    ],
  },
  {
    title: 'Overall Roof Condition',
    questions: [
      'overallRoofCondition', 'roofMaterials', 'roofCovering', 'ageOfRoof', 'shapeOfRoof', 'treeLimbsOnRoof', 'debrisOnRoof', 'solarPanel', 'exposedFelt', 'missingShinglesTiles', 'priorRepairs', 'curlingShingles', 'algaeMoss', 'tarpOnRoof', 'brokenOrCrackedTiles', 'satelliteDish', 'unevenDecking'
    ],
  },
  {
    title: 'Garage/Outbuilding',
    questions: [
      'garageOutbuildingCondition', 'garageType', 'outbuilding', 'outbuildingType', 'fenceDetails', 'garageCondition', 'carportOrAwning', 'carportConstruction', 'fenceCondition'
    ],
  },
  {
    title: 'Dwelling Hazard and Possible Hazards',
    questions: [
      'boardedDoorsWindows', 'overgrownVegetation', 'abandonedVehicles', 'missingDamagedSteps', 'missingDamageRailing', 'sidingDamageHazard', 'hurricaneShutters', 'treeBranch', 'chimneyThroughRoof', 'fireplacePitOutside', 'securityBars', 'fasciaSoffitDamage',
      'swimmingPool', 'divingBoardOrSlide', 'poolFenced', 'trampoline', 'swingSet', 'basketballGoal', 'dog', 'dogType', 'dogSign', 'skateboardOrBikeRamp', 'treeHouse', 'debrisInYard'
    ],
  },
]; 