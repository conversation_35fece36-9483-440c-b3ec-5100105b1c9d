import { getQuestionsFromFirestore } from './questionsService';

/**
 * Get questions data structured for mobile app consumption
 * This service provides the complete questions data with sections for mobile apps
 * @returns {Promise<Object>} Structured questions data for mobile
 */
export const getMobileQuestionsData = async () => {
  try {
    const result = await getQuestionsFromFirestore();
    
    if (!result.success) {
      return {
        success: false,
        error: result.error,
        message: result.message
      };
    }

    const { questions, sections } = result.data;
    
    // Create a map of questions for quick lookup
    const questionsMap = {};
    questions.forEach(question => {
      questionsMap[question.id] = question;
    });

    // Structure data for mobile consumption with sections and their questions
    const structuredSections = sections.map((section, sectionIndex) => ({
      id: `section_${sectionIndex + 1}`,
      title: section.title,
      order: sectionIndex + 1,
      questionCount: section.questions.length,
      questions: section.questions.map((questionId, questionIndex) => {
        const question = questionsMap[questionId];
        if (!question) {
          console.warn(`Question with ID ${questionId} not found in section ${section.title}`);
          return null;
        }
        
        return {
          ...question,
          sectionId: `section_${sectionIndex + 1}`,
          sectionTitle: section.title,
          orderInSection: questionIndex + 1,
          globalOrder: questions.findIndex(q => q.id === questionId) + 1
        };
      }).filter(Boolean) // Remove null entries
    }));

    // Create summary for mobile app
    const summary = {
      totalSections: sections.length,
      totalQuestions: questions.length,
      questionTypes: [...new Set(questions.map(q => q.type))],
      sectionsOverview: sections.map(section => ({
        title: section.title,
        questionCount: section.questions.length
      }))
    };

    return {
      success: true,
      data: {
        sections: structuredSections,
        allQuestions: questions,
        sectionsMetadata: sections,
        summary: summary,
        lastUpdated: new Date().toISOString(),
        version: "1.0"
      },
      message: "Mobile questions data retrieved successfully"
    };

  } catch (error) {
    console.error('Error getting mobile questions data:', error);
    return {
      success: false,
      error: error.message || "unknown-error",
      message: "Failed to get mobile questions data"
    };
  }
};

/**
 * Get questions for a specific section (mobile app helper)
 * @param {string} sectionTitle - Title of the section
 * @returns {Promise<Object>} Section questions data
 */
export const getQuestionsBySection = async (sectionTitle) => {
  try {
    const result = await getMobileQuestionsData();
    
    if (!result.success) {
      return result;
    }

    const section = result.data.sections.find(s => s.title === sectionTitle);
    
    if (!section) {
      return {
        success: false,
        error: "section-not-found",
        message: `Section "${sectionTitle}" not found`
      };
    }

    return {
      success: true,
      data: section,
      message: `Section "${sectionTitle}" retrieved successfully`
    };

  } catch (error) {
    console.error('Error getting questions by section:', error);
    return {
      success: false,
      error: error.message || "unknown-error",
      message: "Failed to get questions by section"
    };
  }
};
