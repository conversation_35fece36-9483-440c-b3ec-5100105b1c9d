import React, { useState } from 'react';
import { ChevronDownIcon } from '@heroicons/react/24/solid';
import { useQuestions } from '../../hooks/useQuestions';
import { saveQuestionsToFirestore } from '../../services/questionsService';

const TextInput = ({ label, name, value, onChange }) => (
  <div className="mb-4">
    <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor={name}>
      {label}
    </label>
    <input
      className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
      id={name}
      type="text"
      placeholder={label}
      name={name}
      value={value || ''}
      onChange={onChange}
    />
  </div>
);

const RadioGroup = ({ label, name, options, value, onChange }) => (
  <div className="mb-4">
    <label className="block text-gray-700 text-sm font-bold mb-2">{label}</label>
    <div className="flex flex-wrap">
      {options.map(option => (
        <div key={option} className="mr-4 mb-2">
          <input
            type="radio"
            id={`${name}_${option}`}
            name={name}
            value={option}
            checked={(value || '') === option}
            onChange={onChange}
            className="mr-2"
          />
          <label htmlFor={`${name}_${option}`}>{option}</label>
        </div>
      ))}
    </div>
  </div>
);

const CheckboxGroup = ({ label, name, options, values, onChange }) => (
  <div className="mb-4">
    <label className="block text-gray-700 text-sm font-bold mb-2">{label}</label>
    <div className="flex flex-wrap">
      {options.map(option => (
        <div key={option} className="mr-4 mb-2">
          <input
            type="checkbox"
            id={`${name}_${option}`}
            name={name}
            value={option}
            checked={Array.isArray(values) && values.includes(option)}
            onChange={onChange}
            className="mr-2"
          />
          <label htmlFor={`${name}_${option}`}>{option}</label>
        </div>
      ))}
    </div>
  </div>
);

const YesNoUnknown = ({ label, name, value, onChange }) => (
  <RadioGroup
    label={label}
    name={name}
    options={['Yes', 'No', 'Unknown']}
    value={value}
    onChange={onChange}
  />
);

const Condition = ({ label, name, value, onChange }) => (
  <RadioGroup
    label={label}
    name={name}
    options={['Excellent', 'Good', 'Fair', 'Poor']}
    value={value}
    onChange={onChange}
  />
);

const generateInitialFormData = (questions) => {
  const formData = {};
  questions.forEach(q => {
    formData[q.id] = q.type === 'checkbox' ? [] : '';
    if (q.hasOther) formData[`${q.id}Other`] = '';
    if (q.type === 'yesnoexplain') formData[`${q.id}Explanation`] = '';
  });
  return formData;
};

const Question = ({ q, formData, handleChange }) => {
  if (q.dependsOn && formData[q.dependsOn] !== q.dependsOnValue) {
    return null;
  }

  const value = formData[q.id];

  switch (q.type) {
    case 'text':
      return <TextInput label={q.label} name={q.id} value={value} onChange={handleChange} />;
    case 'radio':
      return (
        <>
          <RadioGroup label={q.label} name={q.id} options={q.options} value={value} onChange={handleChange} />
          {q.hasOther && value === 'Other' && (
            <TextInput label={`Other ${q.label}`} name={`${q.id}Other`} value={formData[`${q.id}Other`]} onChange={handleChange} />
          )}
        </>
      );
    case 'checkbox':
       return (
        <>
          <CheckboxGroup label={q.label} name={q.id} options={q.options} values={value} onChange={handleChange} />
          {q.hasOther && Array.isArray(value) && value.includes('Other') && (
            <TextInput label={`Other ${q.label}`} name={`${q.id}Other`} value={formData[`${q.id}Other`]} onChange={handleChange} />
          )}
        </>
      );
    case 'condition':
      return <Condition label={q.label} name={q.id} value={value} onChange={handleChange} />;
    case 'yesnounknown':
      return <YesNoUnknown label={q.label} name={q.id} value={value} onChange={handleChange} />;
    case 'yesno':
       return <RadioGroup label={q.label} name={q.id} options={['Yes', 'No']} value={value} onChange={handleChange} />;
    case 'yesnoexplain':
      return (
        <>
          <RadioGroup label={q.label} name={q.id} options={['Yes', 'No']} value={value} onChange={handleChange} />
          {value === 'Yes' && (
            <TextInput label={q.explainLabel || 'If Yes, explain'} name={`${q.id}Explanation`} value={formData[`${q.id}Explanation`]} onChange={handleChange} />
          )}
        </>
      );
    case 'textarea':
      return (
        <div className="mb-4">
          <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor={q.id}>{q.label}</label>
          <textarea
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            id={q.id}
            placeholder={q.label}
            name={q.id}
            value={value}
            onChange={handleChange}
            rows="4"
          />
        </div>
      );
    default:
      return null;
  }
};

const QuestionModal = ({ isOpen, onClose, onSave, question, sections }) => {
  const [editedQuestion, setEditedQuestion] = useState(question);
  const [selectedSection, setSelectedSection] = useState('');

  React.useEffect(() => {
    setEditedQuestion(question);
    // If editing existing question, find which section it belongs to
    if (question && question.id && sections) {
      const questionSection = sections.find(section =>
        section.questions.includes(question.id)
      );
      setSelectedSection(questionSection ? questionSection.title : '');
    } else {
      // For new questions, default to first section
      setSelectedSection(sections && sections.length > 0 ? sections[0].title : '');
    }
  }, [question, sections]);

  if (!isOpen || !editedQuestion) return null;

  const handleSave = () => {
    // Include section information with the question
    const questionWithSection = {
      ...editedQuestion,
      selectedSection: selectedSection
    };
    onSave(questionWithSection);
    onClose();
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    if (name === 'options') {
      setEditedQuestion(prev => ({ ...prev, options: value.split(',').map(s => s.trim()) }));
    } else {
      setEditedQuestion(prev => ({ ...prev, [name]: value }));
    }
  };

  return (
    <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50">
      <div className="bg-white/80 backdrop-blur border-2 border-brand-purple p-8 rounded-2xl shadow-2xl w-full max-w-lg">
        <h2 className="text-2xl font-bold mb-4 text-sidebar-dark">{editedQuestion.id ? 'Edit Question' : 'Add New Question'}</h2>

        {/* Section Selection */}
        <div className="mb-4">
          <label className="block text-gray-700 text-sm font-bold mb-2">Section *</label>
          <select
            value={selectedSection}
            onChange={(e) => setSelectedSection(e.target.value)}
            className="shadow border rounded w-full py-2 px-3 text-gray-700 focus:outline-none focus:shadow-outline"
            required
          >
            <option value="">Select a section...</option>
            {sections && sections.map(section => (
              <option key={section.title} value={section.title}>
                {section.title}
              </option>
            ))}
          </select>
        </div>

        <TextInput label="Question Label *" name="label" value={editedQuestion.label} onChange={handleChange} />

        <div className="mb-4">
          <label className="block text-gray-700 text-sm font-bold mb-2">Question Type *</label>
          <select name="type" value={editedQuestion.type} onChange={handleChange} className="shadow border rounded w-full py-2 px-3 text-gray-700 focus:outline-none focus:shadow-outline">
            <option value="text">Text Input</option>
            <option value="radio">Radio Buttons</option>
            <option value="checkbox">Checkboxes</option>
            <option value="condition">Condition Rating</option>
            <option value="yesnounknown">Yes/No/Unknown</option>
            <option value="yesno">Yes/No</option>
            <option value="yesnoexplain">Yes/No with Explanation</option>
            <option value="textarea">Text Area</option>
          </select>
        </div>

        {(editedQuestion.type === 'radio' || editedQuestion.type === 'checkbox') && (
          <TextInput label="Options (comma-separated) *" name="options" value={(editedQuestion.options || []).join(', ')} onChange={handleChange} />
        )}

        <div className="flex justify-end gap-4 mt-6">
          <button onClick={onClose} className="text-gray-600 font-bold py-2 px-4 rounded">Cancel</button>
          <button
            onClick={handleSave}
            disabled={!selectedSection || !editedQuestion.label}
            className="bg-brand-purple text-white hover:bg-brand-purple/80 focus:ring-2 focus:ring-brand-purple font-bold py-2 px-6 rounded-lg shadow disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Save Question
          </button>
        </div>
      </div>
    </div>
  );
};



// Dropdown for long option lists
const Dropdown = ({ label, name, options, value, onChange }) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleSelect = (selectedValue) => {
    onChange({ target: { name, value: selectedValue } });
    setIsOpen(false);
  };

  return (
    <div className="mb-4">
      <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor={name}>
        {label} <span className="text-xs text-blue-600 font-normal">(Dropdown)</span>
      </label>
      <div className="relative">
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className="relative w-full cursor-pointer rounded border border-blue-200 bg-blue-50 hover:bg-blue-100 py-2 pl-3 pr-10 text-left shadow focus:outline-none focus:ring-2 focus:ring-blue-400 text-gray-700 transition-colors"
        >
          <span className="block truncate">{value || 'Select...'}</span>
          <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
            <ChevronDownIcon className="w-5 h-5 text-blue-400" aria-hidden="true" />
          </span>
        </button>
        
        {isOpen && (
          <div className="absolute z-10 mt-1 w-full bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto">
            {options.map((option) => (
              <button
                key={option}
                type="button"
                onClick={() => handleSelect(option)}
                className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-blue-100 hover:text-blue-900 cursor-pointer"
              >
                {option}
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

// Enhanced Question component
const EnhancedQuestion = ({ q, formData, handleChange }) => {
  if (q.dependsOn && formData[q.dependsOn] !== q.dependsOnValue) {
    return null;
  }
  const value = formData[q.id];
  // Use dropdown for radio or checkbox with more than 5 options
  if ((q.type === 'radio' || q.type === 'checkbox') && q.options && q.options.length > 5) {
    return <Dropdown label={q.label} name={q.id} options={q.options} value={value} onChange={handleChange} />;
  }
  return <Question q={q} formData={formData} handleChange={handleChange} />;
};

export default function QuestionsPage() {
  const {
    questions,
    sections,
    loading,
    error,
    saving,
    deleteQuestion
  } = useQuestions();

  const [draftQuestions, setDraftQuestions] = useState([]);
  const [formData, setFormData] = useState(() => generateInitialFormData([]));
  const [isEditing, setIsEditing] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState(null);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    if (type === 'checkbox') {
      setFormData(prev => ({
        ...prev,
        [name]: checked ? [...(prev[name] || []), value] : (prev[name] || []).filter(v => v !== value)
      }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  /**
   * Save question with section assignment to Firebase
   * @param {Object} question - Question object to save
   * @param {string} sectionTitle - Title of section to assign question to
   */
  const saveQuestionWithSection = async (question, sectionTitle) => {
    try {
      // Create a map of all questions for easy lookup
      const questionsMap = new Map();
      const existingQuestionIndex = questions.findIndex(q => q.id === question.id);

      if (existingQuestionIndex >= 0) {
        // Update existing question
        questions.forEach(q => {
          questionsMap.set(q.id, q.id === question.id ? question : q);
        });
      } else {
        // Add new question to map
        questions.forEach(q => questionsMap.set(q.id, q));
        questionsMap.set(question.id, question);
      }

      // Update sections to include the question in the correct section
      const updatedSections = sections.map(section => {
        if (section.title === sectionTitle) {
          // Add question to target section if not already there
          if (!section.questions.includes(question.id)) {
            return {
              ...section,
              questions: [...section.questions, question.id]
            };
          }
        } else {
          // Remove question from other sections if it was moved
          return {
            ...section,
            questions: section.questions.filter(qId => qId !== question.id)
          };
        }
        return section;
      });

      // Organize questions array based on section order
      const organizedQuestions = [];

      // Add questions in the order they appear in sections
      updatedSections.forEach(section => {
        section.questions.forEach(questionId => {
          const questionObj = questionsMap.get(questionId);
          if (questionObj && !organizedQuestions.find(q => q.id === questionId)) {
            organizedQuestions.push(questionObj);
          }
        });
      });

      // Add any remaining questions that aren't in any section
      questionsMap.forEach((questionObj, questionId) => {
        if (!organizedQuestions.find(q => q.id === questionId)) {
          organizedQuestions.push(questionObj);
        }
      });

      console.log(`Saving question "${question.label}" to section "${sectionTitle}"`);
      console.log('Questions organized by section order');

      // Save both organized questions and sections to Firebase
      const result = await saveQuestionsToFirestore(organizedQuestions, updatedSections);

      if (result.success) {
        console.log('✅ Question saved successfully with section assignment');
        // Update local state to reflect changes
        window.location.reload(); // Temporary solution to refresh data
      } else {
        throw new Error(result.message);
      }

      return result;
    } catch (error) {
      console.error('Error saving question with section:', error);
      throw error;
    }
  };



  const handleDeleteQuestion = async (id) => {
    if (isEditing) {
      // During editing, just update draft (will be saved when "Save Changes" is clicked)
      setDraftQuestions(draftQuestions.filter(q => q.id !== id));
    } else {
      // Delete immediately from Firebase
      const result = await deleteQuestion(id);
      if (!result.success) {
        alert('Failed to delete question: ' + result.message);
      }
    }
  };

  const handleOpenAddModal = () => {
    setEditingQuestion({ id: '', label: '', type: 'text', options: [] });
    setIsModalOpen(true);
  };

  const handleOpenEditModal = (question) => {
    setEditingQuestion({ ...question });
    setIsModalOpen(true);
  };

  const handleSaveQuestion = async (questionToSave) => {
    try {
      // Extract section information and clean question object
      const { selectedSection, ...cleanQuestion } = questionToSave;

      if (!selectedSection) {
        alert('Please select a section for this question.');
        return;
      }

      // Ensure question has an ID
      if (!cleanQuestion.id) {
        cleanQuestion.id = `question_${Date.now()}`;
      }

      if (isEditing) {
        // During editing mode, update draft questions and store section assignment
        let updatedDraftQuestions;

        // Store the section assignment with the question for later processing
        const questionWithSection = { ...cleanQuestion, selectedSection };

        if (draftQuestions.find(q => q.id === cleanQuestion.id)) {
          // Editing existing question
          updatedDraftQuestions = draftQuestions.map(q =>
            q.id === cleanQuestion.id ? questionWithSection : q
          );
        } else {
          // Adding new question
          updatedDraftQuestions = [...draftQuestions, questionWithSection];
        }

        setDraftQuestions(updatedDraftQuestions);

        // Section assignment will be handled when "Save Changes" is clicked
        console.log(`Question "${cleanQuestion.label}" assigned to section "${selectedSection}" in draft mode`);

      } else {
        // Save directly to Firebase with section assignment
        await saveQuestionWithSection(cleanQuestion, selectedSection);
      }
    } catch (error) {
      console.error('Error saving question:', error);
      alert('Failed to save question. Please try again.');
    }
  };

  const handleStartEditing = () => {
    setDraftQuestions([...questions]);
    setIsEditing(true);
  };

  const handleCancelEditing = () => {
    setIsEditing(false);
    setDraftQuestions([]);
  };

  // Function to fix Inspector Information section order
  const fixInspectorSectionOrder = async () => {
    try {
      // Find the Inspector Information section and fix the order
      const updatedSections = sections.map(section => {
        if (section.title === 'Inspector Information') {
          // Ensure the correct order: inspectorName first, then droneNumber
          return {
            ...section,
            questions: ['inspectorName', 'droneNumber']
          };
        }
        return section;
      });

      console.log('Fixing Inspector Information section order...');

      // Save the updated sections to Firebase
      const result = await saveQuestionsToFirestore(questions, updatedSections);

      if (result.success) {
        console.log('✅ Inspector Information section order fixed');
        window.location.reload(); // Refresh to show the changes
      } else {
        console.error('Failed to fix section order:', result.message);
        alert('Failed to fix section order: ' + result.message);
      }
    } catch (error) {
      console.error('Error fixing section order:', error);
      alert('Error fixing section order. Please try again.');
    }
  };

  const handleSaveChanges = async () => {
    try {
      // Process draft questions and handle section assignments
      const questionsMap = new Map();
      const sectionAssignments = new Map(); // Map to track which questions go to which sections

      // Extract questions and their section assignments
      draftQuestions.forEach(question => {
        if (question.selectedSection) {
          // New question with section assignment
          const cleanQuestion = { ...question };
          delete cleanQuestion.selectedSection; // Remove selectedSection from the question object
          questionsMap.set(question.id, cleanQuestion);
          sectionAssignments.set(question.id, question.selectedSection);
        } else {
          // Existing question without section change
          questionsMap.set(question.id, question);
        }
      });

      // Update sections to include new question assignments
      const updatedSections = sections.map(section => {
        let updatedQuestions = [...section.questions];

        // Add new questions assigned to this section
        sectionAssignments.forEach((assignedSection, questionId) => {
          if (assignedSection === section.title && !updatedQuestions.includes(questionId)) {
            updatedQuestions.push(questionId);
          }
        });

        // Remove questions that no longer exist
        const validQuestionIds = new Set(questionsMap.keys());
        updatedQuestions = updatedQuestions.filter(qId => validQuestionIds.has(qId));

        return {
          ...section,
          questions: updatedQuestions
        };
      });

      // Organize questions array based on section order
      const organizedQuestions = [];

      // Add questions in the order they appear in sections
      updatedSections.forEach(section => {
        section.questions.forEach(questionId => {
          const question = questionsMap.get(questionId);
          if (question && !organizedQuestions.find(q => q.id === questionId)) {
            organizedQuestions.push(question);
          }
        });
      });

      // Add any remaining questions that aren't in any section (shouldn't happen, but safety check)
      questionsMap.forEach((question, questionId) => {
        if (!organizedQuestions.find(q => q.id === questionId)) {
          organizedQuestions.push(question);
        }
      });

      console.log(`Saving ${organizedQuestions.length} questions to Firebase with section assignments`);
      console.log('Section assignments:', Object.fromEntries(sectionAssignments));
      console.log('Questions organized by section order');

      // Save both organized questions and updated sections to Firebase
      const result = await saveQuestionsToFirestore(organizedQuestions, updatedSections);

      if (result.success) {
        setIsEditing(false);
        setDraftQuestions([]);
        console.log('✅ All changes saved successfully to Firebase');
        // Refresh the page to show updated data
        window.location.reload();
      } else {
        alert('Failed to save changes: ' + result.message);
      }
    } catch (error) {
      console.error('Error saving changes:', error);
      alert('Failed to save changes. Please try again.');
    }
  };

  // Initialize form data when questions are loaded
  React.useEffect(() => {
    if (questions.length > 0) {
      setFormData(prevFormData => {
        const newFormData = generateInitialFormData(questions);
        // Preserve any existing form data
        return { ...newFormData, ...prevFormData };
      });
    }
  }, [questions]);

  // Show loading state
  if (loading) {
    return (
      <div className="w-full max-w-4xl mx-auto py-8">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-purple mx-auto mb-4"></div>
            <p className="text-sidebar-dark">Loading questions...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="w-full max-w-4xl mx-auto py-8">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-red-800 mb-2">Error Loading Questions</h3>
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="w-full max-w-4xl mx-auto py-8">
        <div className="flex flex-wrap justify-between items-center gap-4 mb-6">
          <h2 className="text-xl sm:text-2xl font-bold text-sidebar-dark">Inspection Questionnaire</h2>
          <div className="flex gap-4">
            {isEditing && (
              <button
                className="bg-brand-purple text-white hover:bg-brand-purple/80 focus:ring-2 focus:ring-brand-purple font-bold py-2 px-4 sm:px-6 rounded-lg shadow transition text-sm sm:text-base"
                onClick={handleOpenAddModal}
              >
                Add New Question
              </button>
            )}
            {!isEditing && (
              <button
                className="bg-brand-purple text-white hover:bg-brand-purple/80 focus:ring-2 focus:ring-brand-purple font-bold py-2 px-4 sm:px-6 rounded-lg shadow transition text-sm sm:text-base"
                onClick={handleStartEditing}
              >
                Edit Questionnaire
              </button>
            )}
          </div>
        </div>

        {isEditing ? (
          <div className="mb-6 p-4 border-2 border-brand-purple rounded-xl bg-white/80 backdrop-blur">
            <h3 className="text-xl font-bold mb-4 text-sidebar-dark">Manage Questions</h3>
            <div className="divide-y divide-brand-purple/30 rounded-lg overflow-hidden">
              {(() => {
                // Organize draft questions by section order for display
                const organizedDraftQuestions = [];
                let questionCounter = 1;

                // Add questions in section order
                sections.forEach(section => {
                  section.questions.forEach(qid => {
                    const draftQuestion = draftQuestions.find(q => q.id === qid);
                    if (draftQuestion && !organizedDraftQuestions.find(q => q.id === qid)) {
                      organizedDraftQuestions.push({ ...draftQuestion, displayIndex: questionCounter++ });
                    }
                  });
                });

                // Add any new questions that aren't in sections yet
                draftQuestions.forEach(q => {
                  if (!organizedDraftQuestions.find(oq => oq.id === q.id)) {
                    organizedDraftQuestions.push({ ...q, displayIndex: questionCounter++ });
                  }
                });

                return organizedDraftQuestions.map((q) => (
                  <div key={q.id} className="flex flex-col sm:flex-row items-start sm:items-center sm:justify-between px-2 py-3 bg-white/60">
                    <span className="mb-2 sm:mb-0 text-sidebar-dark font-medium">
                      {q.displayIndex}. {q.label}
                      {q.selectedSection && <span className="ml-2 text-xs text-blue-600 font-semibold">({q.selectedSection})</span>}
                    </span>
                    <div className="flex items-center gap-4 self-end sm:self-center">
                      <span className="text-xs text-brand-purple font-semibold uppercase tracking-wide">{q.type}</span>
                      <div>
                        <button className="text-brand-purple hover:underline mr-4 font-semibold" onClick={() => handleOpenEditModal(q)}>Edit</button>
                        <button className="text-red-500 hover:underline font-semibold" onClick={() => handleDeleteQuestion(q.id)}>Delete</button>
                      </div>
                    </div>
                  </div>
                ));
              })()}
            </div>
            <div className="flex justify-end gap-4 mt-6">
              <button
                onClick={handleCancelEditing}
                disabled={saving}
                className="text-gray-600 font-bold py-2 px-4 rounded disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveChanges}
                disabled={saving}
                className="bg-brand-purple text-white hover:bg-brand-purple/80 focus:ring-2 focus:ring-brand-purple font-bold py-2 px-6 rounded-lg shadow disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {saving ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Saving...
                  </div>
                ) : (
                  'Save Changes'
                )}
              </button>
            </div>
          </div>
        ) : (
          <form>
            {sections.map(section => (
              <div key={section.title} className="mb-6">
                <div className="rounded-xl shadow bg-white overflow-visible">
                  <div className="bg-blue-50 px-6 py-3 font-semibold text-blue-700 text-lg border-b border-blue-100">{section.title}</div>
                  <div className="p-6">
                    {section.questions.map((qid) => {
                      const q = questions.find(qq => qq.id === qid);
                      if (!q) return null;
                      return <EnhancedQuestion key={q.id} q={q} formData={formData} handleChange={handleChange} />;
                    })}
                  </div>
                </div>
              </div>
            ))}
          </form>
        )}
      </div>
      <QuestionModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSave={handleSaveQuestion}
        question={editingQuestion}
        sections={sections}
      />
    </>
  );
}
