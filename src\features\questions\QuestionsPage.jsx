import React, { useState } from 'react';
import { ChevronDownIcon } from '@heroicons/react/24/solid';
import { useQuestions } from '../../hooks/useQuestions';

const TextInput = ({ label, name, value, onChange }) => (
  <div className="mb-4">
    <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor={name}>
      {label}
    </label>
    <input
      className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
      id={name}
      type="text"
      placeholder={label}
      name={name}
      value={value || ''}
      onChange={onChange}
    />
  </div>
);

const RadioGroup = ({ label, name, options, value, onChange }) => (
  <div className="mb-4">
    <label className="block text-gray-700 text-sm font-bold mb-2">{label}</label>
    <div className="flex flex-wrap">
      {options.map(option => (
        <div key={option} className="mr-4 mb-2">
          <input
            type="radio"
            id={`${name}_${option}`}
            name={name}
            value={option}
            checked={(value || '') === option}
            onChange={onChange}
            className="mr-2"
          />
          <label htmlFor={`${name}_${option}`}>{option}</label>
        </div>
      ))}
    </div>
  </div>
);

const CheckboxGroup = ({ label, name, options, values, onChange }) => (
  <div className="mb-4">
    <label className="block text-gray-700 text-sm font-bold mb-2">{label}</label>
    <div className="flex flex-wrap">
      {options.map(option => (
        <div key={option} className="mr-4 mb-2">
          <input
            type="checkbox"
            id={`${name}_${option}`}
            name={name}
            value={option}
            checked={Array.isArray(values) && values.includes(option)}
            onChange={onChange}
            className="mr-2"
          />
          <label htmlFor={`${name}_${option}`}>{option}</label>
        </div>
      ))}
    </div>
  </div>
);

const YesNoUnknown = ({ label, name, value, onChange }) => (
  <RadioGroup
    label={label}
    name={name}
    options={['Yes', 'No', 'Unknown']}
    value={value}
    onChange={onChange}
  />
);

const Condition = ({ label, name, value, onChange }) => (
  <RadioGroup
    label={label}
    name={name}
    options={['Excellent', 'Good', 'Fair', 'Poor']}
    value={value}
    onChange={onChange}
  />
);

const generateInitialFormData = (questions) => {
  const formData = {};
  questions.forEach(q => {
    formData[q.id] = q.type === 'checkbox' ? [] : '';
    if (q.hasOther) formData[`${q.id}Other`] = '';
    if (q.type === 'yesnoexplain') formData[`${q.id}Explanation`] = '';
  });
  return formData;
};

const Question = ({ q, formData, handleChange }) => {
  if (q.dependsOn && formData[q.dependsOn] !== q.dependsOnValue) {
    return null;
  }

  const value = formData[q.id];

  switch (q.type) {
    case 'text':
      return <TextInput label={q.label} name={q.id} value={value} onChange={handleChange} />;
    case 'radio':
      return (
        <>
          <RadioGroup label={q.label} name={q.id} options={q.options} value={value} onChange={handleChange} />
          {q.hasOther && value === 'Other' && (
            <TextInput label={`Other ${q.label}`} name={`${q.id}Other`} value={formData[`${q.id}Other`]} onChange={handleChange} />
          )}
        </>
      );
    case 'checkbox':
       return (
        <>
          <CheckboxGroup label={q.label} name={q.id} options={q.options} values={value} onChange={handleChange} />
          {q.hasOther && Array.isArray(value) && value.includes('Other') && (
            <TextInput label={`Other ${q.label}`} name={`${q.id}Other`} value={formData[`${q.id}Other`]} onChange={handleChange} />
          )}
        </>
      );
    case 'condition':
      return <Condition label={q.label} name={q.id} value={value} onChange={handleChange} />;
    case 'yesnounknown':
      return <YesNoUnknown label={q.label} name={q.id} value={value} onChange={handleChange} />;
    case 'yesno':
       return <RadioGroup label={q.label} name={q.id} options={['Yes', 'No']} value={value} onChange={handleChange} />;
    case 'yesnoexplain':
      return (
        <>
          <RadioGroup label={q.label} name={q.id} options={['Yes', 'No']} value={value} onChange={handleChange} />
          {value === 'Yes' && (
            <TextInput label={q.explainLabel || 'If Yes, explain'} name={`${q.id}Explanation`} value={formData[`${q.id}Explanation`]} onChange={handleChange} />
          )}
        </>
      );
    case 'textarea':
      return (
        <div className="mb-4">
          <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor={q.id}>{q.label}</label>
          <textarea
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            id={q.id}
            placeholder={q.label}
            name={q.id}
            value={value}
            onChange={handleChange}
            rows="4"
          />
        </div>
      );
    default:
      return null;
  }
};

const QuestionModal = ({ isOpen, onClose, onSave, question }) => {
  const [editedQuestion, setEditedQuestion] = useState(question);

  React.useEffect(() => {
    setEditedQuestion(question);
  }, [question]);

  if (!isOpen || !editedQuestion) return null;

  const handleSave = () => {
    onSave(editedQuestion);
    onClose();
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    if (name === 'options') {
      setEditedQuestion(prev => ({ ...prev, options: value.split(',').map(s => s.trim()) }));
    } else {
      setEditedQuestion(prev => ({ ...prev, [name]: value }));
    }
  };

  return (
    <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50">
      <div className="bg-white/80 backdrop-blur border-2 border-brand-purple p-8 rounded-2xl shadow-2xl w-full max-w-lg">
        <h2 className="text-2xl font-bold mb-4 text-sidebar-dark">{editedQuestion.id ? 'Edit Question' : 'Add New Question'}</h2>
        <TextInput label="Label" name="label" value={editedQuestion.label} onChange={handleChange} />
        <div className="mb-4">
          <label className="block text-gray-700 text-sm font-bold mb-2">Type</label>
          <select name="type" value={editedQuestion.type} onChange={handleChange} className="shadow border rounded w-full py-2 px-3">
            <option value="text">Text</option>
            <option value="radio">Radio</option>
            <option value="checkbox">Checkbox</option>
            <option value="condition">Condition</option>
            <option value="yesnounknown">Yes/No/Unknown</option>
            <option value="yesno">Yes/No</option>
            <option value="yesnoexplain">Yes/No with Explanation</option>
            <option value="textarea">Text Area</option>
          </select>
        </div>
        {(editedQuestion.type === 'radio' || editedQuestion.type === 'checkbox') && (
          <TextInput label="Options (comma-separated)" name="options" value={(editedQuestion.options || []).join(', ')} onChange={handleChange} />
        )}
        <div className="flex justify-end gap-4 mt-6">
          <button onClick={onClose} className="text-gray-600">Cancel</button>
          <button onClick={handleSave} className="bg-brand-purple text-white hover:bg-brand-purple/80 focus:ring-2 focus:ring-brand-purple font-bold py-2 px-6 rounded-lg shadow">Save</button>
        </div>
      </div>
    </div>
  );
};

// Section definitions for grouping
const QUESTION_SECTIONS = [
  {
    title: 'Inspector Information',
    ids: [
      'inspectorName', 'droneNumber',
    ],
  },
  {
    title: 'Insured Information',
    ids: [
      'policyNumber', 'insuredName', 'insuredStreetAddress', 'insuredState', 'insuredZipCode', 'dateOfInspection',
    ],
  },
  {
    title: 'Location Information',
    ids: [
      'neighborhood', 'areaEconomy', 'gatedCommunity', 'propertyVacant', 'nearestBodyOfWater', 'rentalProperty', 'businessOnSite', 'seasonalHome', 'historicProperty', 'nearestDwelling',
    ],
  },
  {
    title: 'Overall Elevation Condition',
    ids: [
      'overallElevationCondition', 'dwellingType', 'yearBuilt', 'typeOfFoundation', 'primaryConstruction', 'numberOfStories', 'livingArea', 'lotSize', 'siding', 'hvac', 'numberOfHVACSystems', 'hvacSerialNumbers', 'guttersAndDownspout', 'fuelTank', 'sidingDamage', 'peelingPaint', 'mildewMoss', 'windowDamage', 'foundationCracks', 'wallCracks', 'chimneyDamage', 'waterDamage', 'underRenovation', 'mainBreakerPanel', 'waterSpicketDamage', 'doorDamage',
    ],
  },
  {
    title: 'Overall Roof Condition',
    ids: [
      'overallRoofCondition', 'roofMaterials', 'roofCovering', 'ageOfRoof', 'shapeOfRoof', 'treeLimbsOnRoof', 'debrisOnRoof', 'solarPanel', 'exposedFelt', 'missingShinglesTiles', 'priorRepairs', 'curlingShingles', 'algaeMoss', 'tarpOnRoof', 'brokenOrCrackedTiles', 'satelliteDish', 'unevenDecking',
    ],
  },
  {
    title: 'Garage/Outbuilding',
    ids: [
      'garageOutbuildingCondition', 'garageType', 'outbuilding', 'outbuildingType', 'fenceDetails', 'garageCondition', 'carportOrAwning', 'carportConstruction', 'fenceCondition',
    ],
  },
  {
    title: 'Dwelling Hazard and Possible Hazard',
    ids: [
      'boardedDoorsWindows', 'overgrownVegetation', 'abandonedVehicles', 'missingDamagedSteps', 'missingDamageRailing', 'sidingDamageHazard', 'hurricaneShutters', 'treeBranch', 'chimneyThroughRoof', 'fireplacePitOutside', 'securityBars', 'fasciaSoffitDamage',
    ],
  },
  {
    title: 'Possible Hazards',
    ids: [
      'swimmingPool', 'divingBoardOrSlide', 'poolFenced', 'trampoline', 'swingSet', 'basketballGoal', 'dog', 'dogType', 'dogSign', 'skateboardOrBikeRamp', 'treeHouse', 'debrisInYard',
    ],
  },
];

// Dropdown for long option lists
const Dropdown = ({ label, name, options, value, onChange }) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleSelect = (selectedValue) => {
    onChange({ target: { name, value: selectedValue } });
    setIsOpen(false);
  };

  return (
    <div className="mb-4">
      <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor={name}>
        {label} <span className="text-xs text-blue-600 font-normal">(Dropdown)</span>
      </label>
      <div className="relative">
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className="relative w-full cursor-pointer rounded border border-blue-200 bg-blue-50 hover:bg-blue-100 py-2 pl-3 pr-10 text-left shadow focus:outline-none focus:ring-2 focus:ring-blue-400 text-gray-700 transition-colors"
        >
          <span className="block truncate">{value || 'Select...'}</span>
          <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
            <ChevronDownIcon className="w-5 h-5 text-blue-400" aria-hidden="true" />
          </span>
        </button>
        
        {isOpen && (
          <div className="absolute z-10 mt-1 w-full bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto">
            {options.map((option) => (
              <button
                key={option}
                type="button"
                onClick={() => handleSelect(option)}
                className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-blue-100 hover:text-blue-900 cursor-pointer"
              >
                {option}
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

// Enhanced Question component
const EnhancedQuestion = ({ q, formData, handleChange }) => {
  if (q.dependsOn && formData[q.dependsOn] !== q.dependsOnValue) {
    return null;
  }
  const value = formData[q.id];
  // Use dropdown for radio or checkbox with more than 5 options
  if ((q.type === 'radio' || q.type === 'checkbox') && q.options && q.options.length > 5) {
    return <Dropdown label={q.label} name={q.id} options={q.options} value={value} onChange={handleChange} />;
  }
  return <Question q={q} formData={formData} handleChange={handleChange} />;
};

export default function QuestionsPage() {
  const {
    questions,
    sections,
    loading,
    error,
    saving,
    updateQuestion,
    deleteQuestion,
    addQuestion,
    saveQuestions
  } = useQuestions();

  const [draftQuestions, setDraftQuestions] = useState([]);
  const [formData, setFormData] = useState(() => generateInitialFormData([]));
  const [isEditing, setIsEditing] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState(null);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    if (type === 'checkbox') {
      setFormData(prev => ({
        ...prev,
        [name]: checked ? [...(prev[name] || []), value] : (prev[name] || []).filter(v => v !== value)
      }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log(formData);
    alert('Form data has been logged to the console.');
  };

  const handleDeleteQuestion = async (id) => {
    if (isEditing) {
      // During editing, just update draft
      setDraftQuestions(draftQuestions.filter(q => q.id !== id));
    } else {
      // Delete from Firebase
      await deleteQuestion(id);
    }
  };

  const handleOpenAddModal = () => {
    setEditingQuestion({ id: '', label: '', type: 'text', options: [] });
    setIsModalOpen(true);
  };

  const handleOpenEditModal = (question) => {
    setEditingQuestion({ ...question });
    setIsModalOpen(true);
  };

  const handleSaveQuestion = async (questionToSave) => {
    if (isEditing) {
      // During editing, update draft
      if (questionToSave.id && draftQuestions.find(q => q.id === questionToSave.id)) {
        setDraftQuestions(draftQuestions.map(q => q.id === questionToSave.id ? questionToSave : q));
      } else {
        setDraftQuestions([...draftQuestions, { ...questionToSave, id: questionToSave.id || `question_${Date.now()}` }]);
      }
    } else {
      // Save directly to Firebase
      if (questionToSave.id && questions.find(q => q.id === questionToSave.id)) {
        await updateQuestion(questionToSave);
      } else {
        await addQuestion(questionToSave);
      }
    }
  };

  const handleStartEditing = () => {
    setDraftQuestions([...questions]);
    setIsEditing(true);
  };

  const handleCancelEditing = () => {
    setIsEditing(false);
    setDraftQuestions([]);
  };

  const handleSaveChanges = async () => {
    try {
      // Save all draft questions to Firebase
      const result = await saveQuestions(draftQuestions);

      if (result.success) {
        setIsEditing(false);
        setDraftQuestions([]);
      } else {
        alert('Failed to save changes: ' + result.message);
      }
    } catch (error) {
      console.error('Error saving changes:', error);
      alert('Failed to save changes. Please try again.');
    }
  };

  // Initialize form data when questions are loaded
  React.useEffect(() => {
    if (questions.length > 0) {
      setFormData(prevFormData => {
        const newFormData = generateInitialFormData(questions);
        // Preserve any existing form data
        return { ...newFormData, ...prevFormData };
      });
    }
  }, [questions]);

  // Show loading state
  if (loading) {
    return (
      <div className="w-full max-w-4xl mx-auto py-8">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-purple mx-auto mb-4"></div>
            <p className="text-sidebar-dark">Loading questions...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="w-full max-w-4xl mx-auto py-8">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-red-800 mb-2">Error Loading Questions</h3>
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="w-full max-w-4xl mx-auto py-8">
        <div className="flex flex-wrap justify-between items-center gap-4 mb-6">
          <h2 className="text-xl sm:text-2xl font-bold text-sidebar-dark">Inspection Questionnaire</h2>
          <div className="flex gap-4">
            {isEditing && (
              <button
                className="bg-brand-purple text-white hover:bg-brand-purple/80 focus:ring-2 focus:ring-brand-purple font-bold py-2 px-4 sm:px-6 rounded-lg shadow transition text-sm sm:text-base"
                onClick={handleOpenAddModal}
              >
                Add New Question
              </button>
            )}
            {!isEditing && (
              <button
                className="bg-brand-purple text-white hover:bg-brand-purple/80 focus:ring-2 focus:ring-brand-purple font-bold py-2 px-4 sm:px-6 rounded-lg shadow transition text-sm sm:text-base"
                onClick={handleStartEditing}
              >
                Edit Questionnaire
              </button>
            )}
          </div>
        </div>

        {isEditing ? (
          <div className="mb-6 p-4 border-2 border-brand-purple rounded-xl bg-white/80 backdrop-blur">
            <h3 className="text-xl font-bold mb-4 text-sidebar-dark">Manage Questions</h3>
            <div className="divide-y divide-brand-purple/30 rounded-lg overflow-hidden">
              {draftQuestions.map((q, index) => (
                <div key={q.id} className="flex flex-col sm:flex-row items-start sm:items-center sm:justify-between px-2 py-3 bg-white/60">
                  <span className="mb-2 sm:mb-0 text-sidebar-dark font-medium">{index + 1}. {q.label}</span>
                  <div className="flex items-center gap-4 self-end sm:self-center">
                    <span className="text-xs text-brand-purple font-semibold uppercase tracking-wide">{q.type}</span>
                    <div>
                      <button className="text-brand-purple hover:underline mr-4 font-semibold" onClick={() => handleOpenEditModal(q)}>Edit</button>
                      <button className="text-red-500 hover:underline font-semibold" onClick={() => handleDeleteQuestion(q.id)}>Delete</button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            <div className="flex justify-end gap-4 mt-6">
              <button
                onClick={handleCancelEditing}
                disabled={saving}
                className="text-gray-600 font-bold py-2 px-4 rounded disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveChanges}
                disabled={saving}
                className="bg-brand-purple text-white hover:bg-brand-purple/80 focus:ring-2 focus:ring-brand-purple font-bold py-2 px-6 rounded-lg shadow disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {saving ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Saving...
                  </div>
                ) : (
                  'Save Changes'
                )}
              </button>
            </div>
          </div>
        ) : (
          <form>
            {sections.map(section => (
              <div key={section.title} className="mb-6">
                <div className="rounded-xl shadow bg-white overflow-visible">
                  <div className="bg-blue-50 px-6 py-3 font-semibold text-blue-700 text-lg border-b border-blue-100">{section.title}</div>
                  <div className="p-6">
                    {section.questions.map((qid) => {
                      const q = questions.find(qq => qq.id === qid);
                      if (!q) return null;
                      return <EnhancedQuestion key={q.id} q={q} formData={formData} handleChange={handleChange} />;
                    })}
                  </div>
                </div>
              </div>
            ))}
          </form>
        )}
      </div>
      <QuestionModal 
        isOpen={isModalOpen} 
        onClose={() => setIsModalOpen(false)} 
        onSave={handleSaveQuestion}
        question={editingQuestion}
      />
    </>
  );
}
