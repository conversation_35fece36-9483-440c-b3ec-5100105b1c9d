import { useState, useEffect } from 'react'
import AdminLayout from './layout/AdminLayout'
import ReportsPage from './features/reports/ReportsPage'
import ExportArchive from './features/reports/ExportArchive'
import QuestionsPage from './features/questions/QuestionsPage'
import SettingsPage from './features/settings/SettingsPage'
import AdminDashboard from './features/dashboard/AdminDashboard'
import LoginPage from './features/auth/LoginPage'
import { AuthProvider, useAuth } from './contexts/AuthContext'
import { logoutUser } from './services/authService'
import './App.css'

// Main App Content Component (handles authenticated state)
function AppContent() {
  const { isAuthenticated, loading } = useAuth();
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [theme, setTheme] = useState(() => localStorage.getItem('theme') || 'light');

  useEffect(() => {
    const root = window.document.documentElement;
    root.classList.remove(theme === 'light' ? 'dark' : 'light');
    root.classList.add(theme);
    localStorage.setItem('theme', theme);
  }, [theme]);

  const handleNavigate = (page) => {
    setCurrentPage(page);
  };

  const handleLogout = async () => {
    try {
      await logoutUser();
      // The AuthContext will automatically handle state updates
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#e3c7f7] via-white to-[#00001b] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-purple mx-auto mb-4"></div>
          <p className="text-sidebar-dark">Loading...</p>
        </div>
      </div>
    );
  }

  const renderPage = () => {
    switch (currentPage) {
      case 'reports':
        return <ReportsPage />;
      case 'questions':
        return <QuestionsPage />;
      case 'archive':
        return <ExportArchive />;
      case 'settings':
        return <SettingsPage theme={theme} setTheme={setTheme} />;
      case 'dashboard':
      default:
        return <AdminDashboard onNavigate={handleNavigate} />;
    }
  };

  if (!isAuthenticated) {
    return <LoginPage />;
  }

  return (
    <AdminLayout active={currentPage} setActive={handleNavigate} onLogout={handleLogout}>
      {renderPage()}
    </AdminLayout>
  );
}

// Main App Component with Firebase Auth Provider
function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App
