import { useState, useEffect } from 'react'
import AdminLayout from './layout/AdminLayout'
import ReportsPage from './features/reports/ReportsPage'
import ExportArchive from './features/reports/ExportArchive'
import QuestionsPage from './features/questions/QuestionsPage'
import SettingsPage from './features/settings/SettingsPage'
import AdminDashboard from './features/dashboard/AdminDashboard'
import LoginPage from './features/auth/LoginPage'
import { AuthProvider, useAuth } from './contexts/AuthContext'
import { logoutUser } from './services/authService'
import './App.css'

const initialReports = [
    {
      id: 'RPT-001',
      reportName: 'Property Condition Report',
      userName: 'Arif Jan',
      property: '123 Main St',
      date: '2025-06-24 10:30',
      status: 'Completed',
      photos: [
        'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=400&q=80',
        'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=400&q=80',
        'https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=400&q=80',
        'https://images.unsplash.com/photo-1500534314209-a25ddb2bd429?auto=format&fit=crop&w=400&q=80',
        'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=400&q=80',
        'https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=400&q=80',
      ],
    questionnaireData: {
      inspectorName: 'Arif Jan',
      droneNumber: 'AL 1',
      policyNumber: 'POLICY-001',
      insuredName: 'John Smith',
      insuredStreetAddress: '123 Main St',
      insuredState: 'California',
      insuredZipCode: '90210',
      dateOfInspection: '2025-06-24',
      neighborhood: 'Suburban',
      areaEconomy: 'Stable',
      gatedCommunity: 'Yes',
      propertyVacant: 'No',
      nearestBodyOfWater: 'Other',
      nearestBodyOfWaterOther: 'Pacific Ocean',
      rentalProperty: 'No',
      businessOnSite: 'No',
      seasonalHome: 'No',
      historicProperty: 'No',
      nearestDwelling: '50',
      overallElevationCondition: 'Average',
      dwellingType: 'Single Family',
      yearBuilt: 'Other',
      yearBuiltOther: '1985',
      typeOfFoundation: 'Slab',
      primaryConstruction: 'Wood Frame',
      numberOfStories: '2',
      livingArea: 'Other',
      livingAreaOther: '2500',
      lotSize: 'Other',
      lotSizeOther: '0.25 acres',
      siding: 'Vinyl',
      hvac: 'Central',
      numberOfHVACSystems: '1',
      hvacSerialNumbers: 'SN-12345',
      guttersAndDownspout: 'Yes',
      fuelTank: 'No',
      sidingDamage: 'No',
      peelingPaint: 'No',
      mildewMoss: 'No',
      windowDamage: 'No',
      foundationCracks: 'No',
      wallCracks: 'No',
      chimneyDamage: 'N/A',
      waterDamage: 'No',
      underRenovation: 'No',
      mainBreakerPanel: 'Yes',
      waterSpicketDamage: 'No',
      doorDamage: 'No',
      overallRoofCondition: 'Average',
      roofMaterials: 'Architectural Shingles',
      roofCovering: 'Asphalt',
      ageOfRoof: '6-10',
      shapeOfRoof: 'Gable',
      treeLimbsOnRoof: 'No',
      debrisOnRoof: 'No',
      solarPanel: 'No',
      exposedFelt: 'No',
      missingShinglesTiles: 'No',
      priorRepairs: 'No',
      curlingShingles: 'No',
      algaeMoss: 'No',
      tarpOnRoof: 'No',
      brokenOrCrackedTiles: 'N/A',
      satelliteDish: 'Yes',
      unevenDecking: 'No',
      garageOutbuildingCondition: 'Average',
      garageType: 'Attached',
      outbuilding: 'No',
      outbuildingType: 'N/A',
      fenceDetails: '6ft Wooden Fence',
      garageCondition: 'Average',
      carportOrAwning: 'No',
      carportConstruction: 'N/A',
      fenceCondition: 'Average',
      boardedDoorsWindows: 'No',
      overgrownVegetation: 'No',
      abandonedVehicles: 'No',
      missingDamagedSteps: 'No',
      missingDamageRailing: 'No',
      sidingDamageHazard: 'No',
      hurricaneShutters: 'No',
      treeBranch: 'No',
      chimneyThroughRoof: 'No',
      fireplacePitOutside: 'No',
      securityBars: 'No',
      fasciaSoffitDamage: 'No',
      swimmingPool: 'Yes',
      divingBoardOrSlide: 'No',
      poolFenced: 'Yes',
      trampoline: 'Yes',
      swingSet: 'Yes',
      basketballGoal: 'Yes',
      dog: 'Yes',
      dogType: 'Medium',
      dogSign: 'Yes',
      skateboardOrBikeRamp: 'No',
      treeHouse: 'No',
      debrisInYard: 'No',
    },
    },
    {
      id: 'RPT-002',
      reportName: 'Initial Site Assessment',
      userName: 'Hanzallah',
      property: '456 Oak Ave',
      date: '2025-06-24 14:15',
      status: 'Pending',
      photos: [],
      questionnaireData: {},
    },
    {
      id: 'RPT-003',
      reportName: 'Property Condition Report',
      userName: 'Hamza',
      property: '789 Pine Ln',
      date: '2025-06-24 09:00',
      status: 'Completed',
      photos: [
        'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=400&q=80',
        'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=400&q=80',
        'https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=400&q=80',
        'https://images.unsplash.com/photo-1500534314209-a25ddb2bd429?auto=format&fit=crop&w=400&q=80',
        'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=400&q=80',
        'https://images.unsplash.com/photo-1465101053361-7630a1c470df?auto=format&fit=crop&w=400&q=80',
        'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=400&q=80',
        'https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=400&q=80',
      ],
    questionnaireData: {
      inspectorName: 'Hamza',
      droneNumber: 'TX 5',
      policyNumber: 'POLICY-002',
      insuredName: 'Jane Doe',
      insuredStreetAddress: '789 Pine Ln',
      insuredState: 'Texas',
      insuredZipCode: '75001',
      dateOfInspection: '2025-06-23',
      neighborhood: 'Rural',
      areaEconomy: 'Stable',
      gatedCommunity: 'No',
      propertyVacant: 'No',
      nearestBodyOfWater: 'Unknown',
      rentalProperty: 'Yes',
      businessOnSite: 'No',
      seasonalHome: 'No',
      historicProperty: 'No',
      nearestDwelling: '200',
      overallElevationCondition: 'Above average',
      dwellingType: 'Single Family',
      yearBuilt: 'Other',
      yearBuiltOther: '2010',
      typeOfFoundation: 'Elevated',
      primaryConstruction: 'Masonry',
      numberOfStories: '1',
      livingArea: 'Other',
      livingAreaOther: '3000',
      lotSize: 'Other',
      lotSizeOther: '2 acres',
      siding: 'Masonry',
      hvac: 'Central',
      numberOfHVACSystems: '2',
      hvacSerialNumbers: 'SN-67890, SN-67891',
      guttersAndDownspout: 'Yes',
      fuelTank: 'Yes',
      sidingDamage: 'No',
      peelingPaint: 'No',
      mildewMoss: 'No',
      windowDamage: 'No',
      foundationCracks: 'No',
      wallCracks: 'No',
      chimneyDamage: 'No',
      waterDamage: 'No',
      underRenovation: 'No',
      mainBreakerPanel: 'Yes',
      waterSpicketDamage: 'No',
      doorDamage: 'No',
      overallRoofCondition: 'Above average',
      roofMaterials: 'Metal',
      roofCovering: 'Metal',
      ageOfRoof: '0-5',
      shapeOfRoof: 'Hip',
      treeLimbsOnRoof: 'No',
      debrisOnRoof: 'No',
      solarPanel: 'Yes',
      exposedFelt: 'No',
      missingShinglesTiles: 'No',
      priorRepairs: 'No',
      curlingShingles: 'No',
      algaeMoss: 'No',
      tarpOnRoof: 'No',
      brokenOrCrackedTiles: 'N/A',
      satelliteDish: 'No',
      unevenDecking: 'No',
      garageOutbuildingCondition: 'Above average',
      garageType: 'Detached',
      outbuilding: 'Yes',
      outbuildingType: 'Metal',
      fenceDetails: 'None',
      garageCondition: 'Above average',
      carportOrAwning: 'Yes',
      carportConstruction: 'Metal',
      fenceCondition: 'N/A',
      boardedDoorsWindows: 'No',
      overgrownVegetation: 'No',
      abandonedVehicles: 'No',
      missingDamagedSteps: 'No',
      missingDamageRailing: 'No',
      sidingDamageHazard: 'No',
      hurricaneShutters: 'Yes',
      treeBranch: 'No',
      chimneyThroughRoof: 'No',
      fireplacePitOutside: 'Yes',
      securityBars: 'No',
      fasciaSoffitDamage: 'No',
      swimmingPool: 'No',
      divingBoardOrSlide: 'N/A',
      poolFenced: 'N/A',
      trampoline: 'No',
      swingSet: 'No',
      basketballGoal: 'No',
      dog: 'No',
      dogType: 'N/A',
      dogSign: 'No',
      skateboardOrBikeRamp: 'No',
      treeHouse: 'No',
      debrisInYard: 'No',
    },
    },
    {
      id: 'RPT-004',
      reportName: 'Archived Site Report',
      userName: 'Farah',
      property: '101 Maple Dr',
      date: '2025-06-24 16:45',
      status: 'Archived',
      photos: [],
      questionnaireData: {},
    },
]

// Main App Content Component (handles authenticated state)
function AppContent() {
  const { isAuthenticated, loading } = useAuth();
  const [reports, setReports] = useState(initialReports);
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [theme, setTheme] = useState(() => localStorage.getItem('theme') || 'light');

  useEffect(() => {
    const root = window.document.documentElement;
    root.classList.remove(theme === 'light' ? 'dark' : 'light');
    root.classList.add(theme);
    localStorage.setItem('theme', theme);
  }, [theme]);

  const handleNavigate = (page) => {
    setCurrentPage(page);
  };

  const handleLogout = async () => {
    try {
      await logoutUser();
      // The AuthContext will automatically handle state updates
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#e3c7f7] via-white to-[#00001b] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-purple mx-auto mb-4"></div>
          <p className="text-sidebar-dark">Loading...</p>
        </div>
      </div>
    );
  }

  const renderPage = () => {
    switch (currentPage) {
      case 'reports':
        return <ReportsPage />;
      case 'questions':
        return <QuestionsPage />;
      case 'archive':
        return <ExportArchive reports={reports} />;
      case 'settings':
        return <SettingsPage theme={theme} setTheme={setTheme} />;
      case 'dashboard':
      default:
        return <AdminDashboard onNavigate={handleNavigate} />;
    }
  };

  if (!isAuthenticated) {
    return <LoginPage />;
  }

  return (
    <AdminLayout active={currentPage} setActive={handleNavigate} onLogout={handleLogout}>
      {renderPage()}
    </AdminLayout>
  );
}

// Main App Component with Firebase Auth Provider
function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App
