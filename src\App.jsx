import { useState, useEffect } from 'react'
import AdminLayout from './layout/AdminLayout'
import ReportsPage from './features/reports/ReportsPage'
import ExportArchive from './features/reports/ExportArchive'
import QuestionsPage from './features/questions/QuestionsPage'
import SettingsPage from './features/settings/SettingsPage'
import AdminDashboard from './features/dashboard/AdminDashboard'
import InspectorsPage from './features/inspectors/InspectorsPage'
import LoginPage from './features/auth/LoginPage'
import { AuthProvider, useAuth } from './contexts/AuthContext'
import { logoutUser } from './services/authService'
import './App.css'



// Inspector Access Denied Component
function InspectorAccessDenied({ onLogout }) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#e3c7f7] via-white to-[#00001b] flex items-center justify-center p-4">
      <div className="bg-white/80 backdrop-blur-md border-2 border-brand-purple rounded-2xl shadow-2xl max-w-md w-full p-8 text-center">
        <div className="mb-6">
          <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-sidebar-dark mb-2">Access Denied</h1>
          <p className="text-gray-600 mb-6">
            Inspector accounts cannot access the admin dashboard. Please use the mobile app to access your inspection tools.
          </p>
        </div>
        
        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-semibold text-blue-800 mb-2">For Inspectors:</h3>
            <p className="text-sm text-blue-700">
              Download and use the mobile inspection app to create and manage your inspection reports.
            </p>
          </div>
          
          <button
            onClick={onLogout}
            className="w-full bg-brand-purple text-white hover:bg-brand-purple/80 font-bold py-3 px-4 rounded-lg shadow transition-colors"
          >
            Logout
          </button>
        </div>
      </div>
    </div>
  );
}

// Main App Content Component (handles authenticated state)
function AppContent() {
  const { isAuthenticated, loading, canAccessAdminDashboard, userRole } = useAuth();
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [theme, setTheme] = useState(() => localStorage.getItem('theme') || 'light');

  useEffect(() => {
    const root = window.document.documentElement;
    root.classList.remove(theme === 'light' ? 'dark' : 'light');
    root.classList.add(theme);
    localStorage.setItem('theme', theme);
  }, [theme]);

  const handleNavigate = (page) => {
    setCurrentPage(page);
  };

  const handleLogout = async () => {
    try {
      await logoutUser();
      // The AuthContext will automatically handle state updates
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#e3c7f7] via-white to-[#00001b] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-purple mx-auto mb-4"></div>
          <p className="text-sidebar-dark">Loading...</p>
        </div>
      </div>
    );
  }

  // Show login page if not authenticated
  if (!isAuthenticated) {
    return <LoginPage />;
  }

  // Show access denied page for inspectors (both active and inactive)
  if (isAuthenticated && (userRole === 'inspector' || userRole === 'inspector_inactive') && !canAccessAdminDashboard) {
    return <InspectorAccessDenied onLogout={handleLogout} />;
  }

  const renderPage = () => {
    switch (currentPage) {
      case 'reports':
        return <ReportsPage />;
      case 'questions':
        return <QuestionsPage />;
      case 'archive':
        return <ExportArchive />;
      case 'inspectors':
        return <InspectorsPage />;
      case 'settings':
        return <SettingsPage theme={theme} setTheme={setTheme} />;
      case 'dashboard':
      default:
        return <AdminDashboard onNavigate={handleNavigate} />;
    }
  };

  return (
    <AdminLayout active={currentPage} setActive={handleNavigate} onLogout={handleLogout}>
      {renderPage()}
    </AdminLayout>
  );
}

// Main App Component with Firebase Auth Provider
function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App
