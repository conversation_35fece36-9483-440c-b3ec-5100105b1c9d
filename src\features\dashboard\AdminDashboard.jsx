import { ArrowRightIcon } from '@heroicons/react/24/outline';

export default function AdminDashboard({ onNavigate }) {
  const tileClass = `
    bg-white/70 border-2 border-brand-purple rounded-2xl p-8 text-center cursor-pointer shadow-lg backdrop-blur-md
    transition-all duration-200 ease-in-out
    hover:scale-105 hover:shadow-2xl hover:-translate-y-1 hover:bg-brand-purple/60
    flex items-center justify-between group
  `;

  const cardData = [
    { label: 'View Reports', nav: 'reports' },
    { label: 'View Archived Reports', nav: 'archive' },
    { label: 'Edit Questions', nav: 'questions' },
    { label: 'Manage Inspectors', nav: 'inspectors' },
    { label: 'Settings', nav: 'settings' },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8 py-8">
      {cardData.map(card => (
        <div
          key={card.nav}
          className={tileClass}
          onClick={() => onNavigate(card.nav)}
        >
          <span className="text-xl font-semibold text-sidebar-dark group-hover:text-sidebar-dark transition-colors duration-200">{card.label}</span>
          <ArrowRightIcon className="w-7 h-7 ml-4 text-brand-purple group-hover:text-sidebar-dark transition-colors duration-200" />
        </div>
      ))}
    </div>
  );
} 