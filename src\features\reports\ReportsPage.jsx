import React, { useState } from 'react';
import ReportDetails from './ReportDetails';
import { EyeIcon, ArrowDownTrayIcon, ArchiveBoxArrowDownIcon } from '@heroicons/react/24/outline';
import jsPDF from 'jspdf';
import { useReports } from '../../hooks/useReports';
import { useQuestions } from '../../hooks/useQuestions';

export default function ReportsPage() {
  // Firebase hooks
  const { reports, loading, error, archiveReportById } = useReports();
  const { questions, sections } = useQuestions();

  // Local state
  const [filterReportName, setFilterReportName] = useState('');
  const [filterFrom, setFilterFrom] = useState('');
  const [filterTo, setFilterTo] = useState('');
  const [selected, setSelected] = useState(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [showArchived, setShowArchived] = useState(false);

  const filtered = reports.filter(r => {
    // Safely extract date - handle different date formats
    let date;
    try {
      if (r.date) {
        // Handle ISO string dates
        if (r.date.includes('T')) {
          date = r.date.split('T')[0];
        } 
        // Handle space-separated dates
        else if (r.date.includes(' ')) {
          date = r.date.split(' ')[0];
        }
        // Handle date-only strings
        else {
          date = r.date;
        }
      } else {
        date = new Date().toISOString().split('T')[0]; // fallback to today
      }
    } catch (error) {
      console.warn('Date parsing error for report:', r.id, error);
      date = new Date().toISOString().split('T')[0];
    }

    const matchReportName = !filterReportName || (r.reportName && r.reportName.toLowerCase().includes(filterReportName.toLowerCase()));
    const afterFrom = !filterFrom || date >= filterFrom;
    const beforeTo = !filterTo || date <= filterTo;
    const matchArchive = showArchived ? r.status === 'Archived' : r.status !== 'Archived';
    return matchReportName && afterFrom && beforeTo && matchArchive;
  });

  // Helper to fetch image as data URL (needed for PDF images)
  const getImageDataUrl = async (url) => {
    try {
      const res = await fetch(url);
      const blob = await res.blob();
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result);
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      throw error;
    }
  };

  const handleDownloadPDF = async (report) => {
    try {
      console.log('Starting PDF generation for report:', report.id);
      console.log('Report images:', report.images);
      
      const doc = new jsPDF({ orientation: 'p', unit: 'pt', format: 'a4' });
      let y = 40;

    // Helper function to check if we need a new page
    const checkNewPage = (requiredSpace = 50) => {
      if (y + requiredSpace > 750) {
        doc.addPage();
        y = 40;
      }
    };

    // Extract insured information (same as app)
    const getInsuredInfo = (responses) => {
      if (!responses) return {};
      const info = {};
      
      for (const [key, response] of Object.entries(responses)) {
        if (typeof response === 'object' && response.question_text && response.value) {
          const questionText = response.question_text.toLowerCase();
          
          if ((questionText.includes('insured') && questionText.includes('name')) ||
              (questionText.includes('property') && questionText.includes('owner'))) {
            info.insuredName = response.value;
          }
          if ((questionText.includes('address') && questionText.includes('street')) ||
              (questionText.includes('property') && questionText.includes('address'))) {
            info.address = response.value;
          }
          if (questionText.includes('state') && !questionText.includes('estate')) {
            info.state = response.value;
          }
          if (questionText.includes('zip') || questionText.includes('postal')) {
            info.zipCode = response.value;
          }
          if (questionText.includes('policy') && questionText.includes('number')) {
            info.policyNumber = response.value;
          }
        }
      }
      return info;
    };

    const hasQuestionnaireData = report.questionnaire_responses || report.questionnaireData;
    const insuredInfo = getInsuredInfo(hasQuestionnaireData);

    // Header
    doc.setFontSize(20);
    doc.text('Inspection Report', 40, y);
    y += 30;
    
    // Insured Information
    doc.setFontSize(12);
    doc.text(`Insured: ${insuredInfo.insuredName || 'Not specified'}`, 40, y); y += 16;
    doc.text(`Address: ${insuredInfo.address || 'Not specified'}`, 40, y); y += 16;
    doc.text(`State: ${insuredInfo.state || 'Not specified'}`, 40, y); y += 16;
    doc.text(`Zip Code: ${insuredInfo.zipCode || 'Not specified'}`, 40, y); y += 16;
    doc.text(`Policy Number: ${insuredInfo.policyNumber || 'Not specified'}`, 40, y); y += 16;
    doc.text(`Inspection Date: ${report.date ? report.date.split('T')[0] : 'Not specified'}`, 40, y); y += 16;
    
    const inspectorText = report.inspectorName ? 
      `Inspector: ${report.inspectorName} (ID: ${report.inspectorId})` : 
      `Inspector: ${report.inspectorId || 'Not specified'}`;
    doc.text(inspectorText, 40, y); y += 24;
    
    // Introduction paragraph
    doc.setFontSize(11);
    const introText = `Dear Mercury Insurance,\n\nPlease see the below underwriting inspection report outlining the overview of the property at ${insuredInfo.address || 'Property Address Not Specified'}, ${insuredInfo.state || 'State Not Specified'} ${insuredInfo.zipCode || 'Zip Not Specified'}. The assignment was submitted to Safe Harbor with a request to complete an exterior underwriting inspection. Thank you for the opportunity to provide you with this report.`;
    doc.text(introText, 40, y, { maxWidth: 500 });
    y += 50;

    // Questionnaire Section
    checkNewPage(100);
    const questionMap = Object.fromEntries(questions.map(q => [q.id, q]));
    doc.setFontSize(16);
    doc.text('Answered Questionnaire', 40, y); y += 18;
    
    sections.forEach(section => {
      checkNewPage(50);
      doc.setFontSize(13);
      doc.setTextColor(30, 64, 175);
      doc.text(section.title, 40, y); y += 14;
      doc.setTextColor(0, 0, 0);
      
      section.questions.forEach(qid => {
        const q = questionMap[qid];
        if (!q) return;
        
        checkNewPage(20);
        
        const responses = hasQuestionnaireData || {};
        let answer = 'No';
        
        if (responses[qid] && typeof responses[qid] === 'object' && responses[qid].value !== undefined) {
          answer = responses[qid].value;
        } else if (responses[qid]) {
          answer = responses[qid];
        }
        
        if (answer && answer !== 'No') {
          if (Array.isArray(answer)) {
            answer = answer.join(', ');
          } else {
            answer = String(answer);
          }
        }
        
        doc.setFontSize(11);
        doc.text(`${q.label}:`, 50, y);
        doc.text(answer, 300, y);
        y += 13;
      });
      y += 6;
    });
    
    // Summary Section
    checkNewPage(60);
    doc.setFontSize(13);
    doc.setTextColor(30, 64, 175);
    doc.text('Summary', 40, y); y += 14;
    doc.setTextColor(0, 0, 0);
    doc.setFontSize(11);
    
    const summaryText = hasQuestionnaireData?.summary || 
      "The property is generally in good condition with no evidence of roof damage, exterior elevation damage was noted to right elevation window along with tipping hazard on front walkway. The property has been inspected, and the surrounding area is free of debris. A six-foot wooden fence enclosing the property's rear is in good condition. No additional recommendations are being made at this time.";
    doc.text(summaryText, 50, y, { maxWidth: 500 });
    y += 50;

    // Photos Section - Clean and Optimized
    checkNewPage(100);
    doc.setFontSize(16);
    doc.text('Attached Photos:', 40, y); y += 18;
    
    console.log('Processing images for PDF:', report.images);
    
    if (report.images && Object.keys(report.images).length > 0) {
      console.log('Found images, processing categories...');
      const categoryDisplayNames = {
        'primary_risk': 'Primary Risk Photo',
        'front_elevation': 'Front Elevation',
        'left_elevation': 'Left Elevation', 
        'rear_elevation': 'Rear Elevation',
        'right_elevation': 'Right Elevation',
        'roof': 'Roof',
        'additional': 'Additional Photos'
      };
      
      for (const [category, categoryImages] of Object.entries(report.images)) {
        console.log(`Processing category: ${category}`, categoryImages);
        const displayName = categoryDisplayNames[category] || category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
        
        // Extract image URLs
        let imageUrls = [];
        if (Array.isArray(categoryImages)) {
          imageUrls = categoryImages;
        } else if (typeof categoryImages === 'object' && categoryImages !== null) {
          imageUrls = Object.values(categoryImages).filter(url => typeof url === 'string');
        } else if (typeof categoryImages === 'string') {
          imageUrls = [categoryImages];
        }
        
        console.log(`Extracted ${imageUrls.length} image URLs for ${category}:`, imageUrls);
        
        if (imageUrls.length === 0) continue;
        
        // Category header
        checkNewPage(120);
        doc.setFontSize(13);
        doc.setTextColor(30, 64, 175);
        doc.text(displayName, 50, y); y += 13;
        doc.setFontSize(10);
        doc.setTextColor(100, 100, 100);
        doc.text(`${imageUrls.length} photo${imageUrls.length !== 1 ? 's' : ''} in this category`, 55, y); y += 12;
        doc.setTextColor(0, 0, 0);
        
        // Process ALL images in this category
        let x = 50;
        let imagesInRow = 0;
        const maxImagesPerRow = 2;
        const imageWidth = 120;
        const imageHeight = 90;
        const imageSpacing = 150;
        
        for (const imageUrl of imageUrls) {
          console.log(`Processing image: ${imageUrl}`);
          
          if (imagesInRow >= maxImagesPerRow) {
            y += imageHeight + 30;
            x = 50;
            imagesInRow = 0;
            checkNewPage(120);
          }
          
          try {
            console.log(`Fetching image data for: ${imageUrl}`);
            const imgData = await getImageDataUrl(imageUrl);
            console.log(`Successfully fetched image data, adding to PDF`);
            doc.addImage(imgData, 'JPEG', x, y, imageWidth, imageHeight);
            
            // Image labels
            doc.setFontSize(8);
            doc.text(`Dwelling > Exterior > ${displayName}`, x, y + imageHeight + 10);
            doc.text(`Date: ${report.date ? report.date.split('T')[0] : 'Unknown'}`, x, y + imageHeight + 20);
          } catch (e) {
            console.error(`Failed to load image: ${imageUrl}`, e);
            // Fallback for failed images
            doc.rect(x, y, imageWidth, imageHeight);
            doc.setFontSize(8);
            doc.text('Image Load Failed', x + 10, y + imageHeight/2);
            doc.text(`Dwelling > Exterior > ${displayName}`, x, y + imageHeight + 10);
            doc.text(`Date: ${report.date ? report.date.split('T')[0] : 'Unknown'}`, x, y + imageHeight + 20);
          }
          
          x += imageSpacing;
          imagesInRow++;
        }
        
        y += imageHeight + 50;
      }
    } else {
      console.log('No images found in report');
      doc.setFontSize(11);
      doc.setTextColor(100, 100, 100);
      doc.text('No photos available for this report', 50, y);
    }

      console.log('PDF generation completed successfully');
      doc.save(`inspection-report-${report.id}.pdf`);
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Failed to generate PDF. Please try again.');
    }
  };

  const handleArchive = async (id) => {
    const result = await archiveReportById(id);
    if (!result.success) {
      alert('Failed to archive report: ' + result.message);
    }
  };

  return (
    <div className="w-full max-w-5xl mx-auto py-8">
      <div className="flex flex-col sm:flex-row sm:items-end gap-4 mb-6">
        <div>
          <label className="block text-xs font-medium mb-1 text-gray-600">Date From</label>
          <input type="date" className="border border-gray-300 rounded px-3 py-2 w-36 focus:ring-2 focus:ring-blue-200 transition" value={filterFrom} onChange={e => setFilterFrom(e.target.value)} />
        </div>
        <div>
          <label className="block text-xs font-medium mb-1 text-gray-600">Date To</label>
          <input type="date" className="border border-gray-300 rounded px-3 py-2 w-36 focus:ring-2 focus:ring-blue-200 transition" value={filterTo} onChange={e => setFilterTo(e.target.value)} />
        </div>
        <div>
          <label className="block text-xs font-medium mb-1 text-gray-600">Report Name</label>
          <input type="text" className="border border-gray-300 rounded px-3 py-2 w-40 focus:ring-2 focus:ring-blue-200 transition" placeholder="Report name..." value={filterReportName} onChange={e => setFilterReportName(e.target.value)} />
        </div>
        <div className="flex items-center gap-2 mt-2 sm:mt-0">
          <span className="text-xs text-gray-600">Show Archived</span>
          <button
            className={`w-10 h-6 flex items-center rounded-full p-1 transition-colors duration-200 ${showArchived ? 'bg-blue-600' : 'bg-gray-300'}`}
            onClick={() => setShowArchived(a => !a)}
            aria-label="Toggle archived reports"
          >
            <span
              className={`bg-white w-4 h-4 rounded-full shadow transform transition-transform duration-200 ${showArchived ? 'translate-x-4' : ''}`}
            />
          </button>
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading reports...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-red-800 mb-2">Error Loading Reports</h3>
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      )}



      {/* Reports Table */}
      {!loading && !error && (
        <div className="overflow-x-auto bg-white rounded-lg shadow-md border border-gray-200">
        <table className="min-w-full text-sm">
          <thead className="hidden md:table-header-group bg-gray-50">
            <tr className="text-xs text-gray-500 uppercase tracking-wider">
              <th className="py-3 px-4 font-medium text-center">#</th>
              <th className="py-3 px-4 font-medium text-left">Date</th>
              <th className="py-3 px-4 font-medium text-left">Report Name</th>
              <th className="py-3 px-4 font-medium text-left">User Name</th>
              <th className="py-3 px-4 font-medium text-center">Status</th>
              <th className="py-3 px-4 font-medium text-center">View</th>
              <th className="py-3 px-4 font-medium text-center">Actions</th>
            </tr>
          </thead>
          <tbody className="space-y-4 md:space-y-0">
            {filtered.map((report, idx) => (
              <tr key={report.id} className="block md:table-row bg-white rounded-lg shadow-md md:shadow-none md:border-b md:border-gray-200">
                <td className="p-4 block md:table-cell md:text-center border-b md:border-none">
                  <span className="font-semibold md:hidden"># </span>
                  <span className="md:float-none">{String(idx + 1).padStart(2, '0')}</span>
                </td>
                <td className="p-4 block md:table-cell md:text-left border-b md:border-none">
                   <span className="font-semibold md:hidden">Date: </span>
                   <span className="md:float-none">{
                     (() => {
                       try {
                         if (report.date) {
                           if (report.date.includes('T')) {
                             return report.date.split('T')[0];
                           } else if (report.date.includes(' ')) {
                             return report.date.split(' ')[0];
                           } else {
                             return report.date;
                           }
                         }
                         return 'No date';
                       } catch (error) {
                         return 'Invalid date';
                       }
                     })()
                   }</span>
                </td>
                <td className="p-4 block md:table-cell md:text-left border-b md:border-none">
                   <span className="font-semibold md:hidden">Report Name: </span>
                   <span className="md:float-none">{report.reportName}</span>
                </td>
                <td className="p-4 block md:table-cell md:text-left border-b md:border-none">
                   <span className="font-semibold md:hidden">User Name: </span>
                   <div className="md:float-none">
                     {report.inspectorName ? (
                       <>
                         <div className="font-medium text-gray-900">{report.inspectorName}</div>
                         <div className="text-xs text-gray-500">ID: {report.inspectorId}</div>
                       </>
                     ) : (
                       <div className="text-gray-600">{report.inspectorId}</div>
                     )}
                   </div>
                </td>
                <td className="p-4 block md:table-cell md:text-center border-b md:border-none">
                   <span className="font-semibold md:hidden">Status: </span>
                   <span className="md:float-none">{report.status}</span>
                </td>
                <td className="p-4 block md:table-cell md:text-center border-b md:border-none">
                  <div className="flex justify-between items-center md:justify-center">
                    <span className="font-semibold md:hidden">View</span>
                    <button
                      title="View"
                      onClick={() => { setSelected(report); setModalOpen(true); }}
                      className="inline-flex items-center justify-center rounded bg-brand-purple text-white hover:bg-brand-purple/80 focus:ring-2 focus:ring-brand-purple w-9 h-9 p-2 transition"
                    >
                      <EyeIcon className="w-5 h-5" />
                    </button>
                  </div>
                </td>
                <td className="p-4 block md:table-cell md:text-center">
                  <div className="flex justify-between items-center md:justify-center">
                    <span className="font-semibold md:hidden">Actions</span>
                    <div className="flex flex-row items-center gap-2 md:justify-center">
                      <button
                        title="Download PDF"
                        onClick={() => handleDownloadPDF(report)}
                        className="inline-flex items-center justify-center rounded bg-brand-purple text-white hover:bg-brand-purple/80 focus:ring-2 focus:ring-brand-purple w-9 h-9 p-2 transition"
                      >
                        <ArrowDownTrayIcon className="w-5 h-5" />
                      </button>
                      <button
                        title="Archive"
                        onClick={() => handleArchive(report.id)}
                        className={`inline-flex items-center justify-center rounded bg-brand-purple text-white hover:bg-brand-purple/80 focus:ring-2 focus:ring-brand-purple w-9 h-9 p-2 transition ${report.status === 'Archived' ? 'opacity-50 cursor-not-allowed' : ''}`}
                        disabled={report.status === 'Archived'}
                      >
                        <ArchiveBoxArrowDownIcon className="w-5 h-5" />
                      </button>
                    </div>
                  </div>
                </td>
              </tr>
            ))}
            {filtered.length === 0 && (
              <tr className="md:table-row">
                <td colSpan={7} className="text-center py-8 text-gray-400">No reports found.</td>
              </tr>
            )}
          </tbody>
        </table>
        </div>
      )}

      <ReportDetails
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        report={selected}
      />
    </div>
  );
} 