import React, { useState } from 'react';
import ReportDetails from './ReportDetails';
import { EyeIcon, ArrowDownTrayIcon, ArchiveBoxArrowDownIcon } from '@heroicons/react/24/outline';
import jsPDF from 'jspdf';

export default function ReportsPage({ reports, setReports, questions }) {
  const [filterReportName, setFilterReportName] = useState('');
  const [filterFrom, setFilterFrom] = useState('');
  const [filterTo, setFilterTo] = useState('');
  const [selected, setSelected] = useState(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [showArchived, setShowArchived] = useState(false);

  const filtered = reports.filter(r => {
    const date = r.date.split(' ')[0];
    const matchReportName = !filterReportName || r.reportName.toLowerCase().includes(filterReportName.toLowerCase());
    const afterFrom = !filterFrom || date >= filterFrom;
    const beforeTo = !filterTo || date <= filterTo;
    const matchArchive = showArchived ? r.status === 'Archived' : r.status !== 'Archived';
    return matchReportName && afterFrom && beforeTo && matchArchive;
  });

  const handleDownloadPDF = async (report) => {
    const pdf = new jsPDF({ orientation: 'p', unit: 'pt', format: 'a4' });
    let y = 40;
    pdf.setFontSize(16);
    pdf.text('Inspection Report', 40, y);
    y += 30;
    pdf.setFontSize(12);
    pdf.text(`ID: ${report.id}`, 40, y);
    pdf.text(`User: ${report.userName}`, 200, y);
    pdf.text(`Date: ${report.date}`, 400, y);
    y += 20;
    pdf.text(`Status: ${report.status}`, 40, y);
    y += 20;
    pdf.setFontSize(11);
    if (report.qa && report.qa.length > 0) {
      pdf.text('Q&A:', 40, y);
      y += 16;
      report.qa.forEach(q => {
        pdf.text(`- ${q.q}: ${q.a}`, 60, y);
        y += 14;
      });
    }
    pdf.save(`${report.id}_report.pdf`);
  };

  const handleArchive = (id) => {
    setReports(reports => reports.map(r => r.id === id ? { ...r, status: 'Archived' } : r));
  };

  return (
    <div className="w-full max-w-5xl mx-auto py-8">
      <div className="flex flex-col sm:flex-row sm:items-end gap-4 mb-6">
        <div>
          <label className="block text-xs font-medium mb-1 text-gray-600">Date From</label>
          <input type="date" className="border border-gray-300 rounded px-3 py-2 w-36 focus:ring-2 focus:ring-blue-200 transition" value={filterFrom} onChange={e => setFilterFrom(e.target.value)} />
        </div>
        <div>
          <label className="block text-xs font-medium mb-1 text-gray-600">Date To</label>
          <input type="date" className="border border-gray-300 rounded px-3 py-2 w-36 focus:ring-2 focus:ring-blue-200 transition" value={filterTo} onChange={e => setFilterTo(e.target.value)} />
        </div>
        <div>
          <label className="block text-xs font-medium mb-1 text-gray-600">Report Name</label>
          <input type="text" className="border border-gray-300 rounded px-3 py-2 w-40 focus:ring-2 focus:ring-blue-200 transition" placeholder="Report name..." value={filterReportName} onChange={e => setFilterReportName(e.target.value)} />
        </div>
        <div className="flex items-center gap-2 mt-2 sm:mt-0">
          <span className="text-xs text-gray-600">Show Archived</span>
          <button
            className={`w-10 h-6 flex items-center rounded-full p-1 transition-colors duration-200 ${showArchived ? 'bg-blue-600' : 'bg-gray-300'}`}
            onClick={() => setShowArchived(a => !a)}
            aria-label="Toggle archived reports"
          >
            <span
              className={`bg-white w-4 h-4 rounded-full shadow transform transition-transform duration-200 ${showArchived ? 'translate-x-4' : ''}`}
            />
          </button>
        </div>
      </div>
      <div className="overflow-x-auto bg-white rounded-lg shadow-md border border-gray-200">
        <table className="min-w-full text-sm">
          <thead className="hidden md:table-header-group bg-gray-50">
            <tr className="text-xs text-gray-500 uppercase tracking-wider">
              <th className="py-3 px-4 font-medium text-center">#</th>
              <th className="py-3 px-4 font-medium text-left">Date</th>
              <th className="py-3 px-4 font-medium text-left">Report Name</th>
              <th className="py-3 px-4 font-medium text-left">User Name</th>
              <th className="py-3 px-4 font-medium text-center">Status</th>
              <th className="py-3 px-4 font-medium text-center">View</th>
              <th className="py-3 px-4 font-medium text-center">Actions</th>
            </tr>
          </thead>
          <tbody className="space-y-4 md:space-y-0">
            {filtered.map((report, idx) => (
              <tr key={report.id} className="block md:table-row bg-white rounded-lg shadow-md md:shadow-none md:border-b md:border-gray-200">
                <td className="p-4 block md:table-cell md:text-center border-b md:border-none">
                  <span className="font-semibold md:hidden"># </span>
                  <span className="md:float-none">{String(idx + 1).padStart(2, '0')}</span>
                </td>
                <td className="p-4 block md:table-cell md:text-left border-b md:border-none">
                   <span className="font-semibold md:hidden">Date: </span>
                   <span className="md:float-none">{report.date.split(' ')[0]}</span>
                </td>
                <td className="p-4 block md:table-cell md:text-left border-b md:border-none">
                   <span className="font-semibold md:hidden">Report Name: </span>
                   <span className="md:float-none">{report.reportName}</span>
                </td>
                <td className="p-4 block md:table-cell md:text-left border-b md:border-none">
                   <span className="font-semibold md:hidden">User Name: </span>
                   <span className="md:float-none">{report.userName}</span>
                </td>
                <td className="p-4 block md:table-cell md:text-center border-b md:border-none">
                   <span className="font-semibold md:hidden">Status: </span>
                   <span className="md:float-none">{report.status}</span>
                </td>
                <td className="p-4 block md:table-cell md:text-center border-b md:border-none">
                  <div className="flex justify-between items-center md:justify-center">
                    <span className="font-semibold md:hidden">View</span>
                    <button
                      title="View"
                      onClick={() => { setSelected(report); setModalOpen(true); }}
                      className="inline-flex items-center justify-center rounded bg-brand-purple text-white hover:bg-brand-purple/80 focus:ring-2 focus:ring-brand-purple w-9 h-9 p-2 transition"
                    >
                      <EyeIcon className="w-5 h-5" />
                    </button>
                  </div>
                </td>
                <td className="p-4 block md:table-cell md:text-center">
                  <div className="flex justify-between items-center md:justify-center">
                    <span className="font-semibold md:hidden">Actions</span>
                    <div className="flex flex-row items-center gap-2 md:justify-center">
                      <button
                        title="Download PDF"
                        onClick={() => handleDownloadPDF(report)}
                        className="inline-flex items-center justify-center rounded bg-brand-purple text-white hover:bg-brand-purple/80 focus:ring-2 focus:ring-brand-purple w-9 h-9 p-2 transition"
                      >
                        <ArrowDownTrayIcon className="w-5 h-5" />
                      </button>
                      <button
                        title="Archive"
                        onClick={() => handleArchive(report.id)}
                        className={`inline-flex items-center justify-center rounded bg-brand-purple text-white hover:bg-brand-purple/80 focus:ring-2 focus:ring-brand-purple w-9 h-9 p-2 transition ${report.status === 'Archived' ? 'opacity-50 cursor-not-allowed' : ''}`}
                        disabled={report.status === 'Archived'}
                      >
                        <ArchiveBoxArrowDownIcon className="w-5 h-5" />
                      </button>
                    </div>
                  </div>
                </td>
              </tr>
            ))}
            {filtered.length === 0 && (
              <tr className="md:table-row">
                <td colSpan={7} className="text-center py-8 text-gray-400">No reports found.</td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      <ReportDetails
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        report={selected}
        questions={questions}
      />
    </div>
  );
} 