import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
  where,
  serverTimestamp
} from "firebase/firestore";
import { 
  createUserWithEmailAndPassword,
  updateProfile,
  signOut,
  signInWithEmailAndPassword,
  onAuthStateChanged
} from "firebase/auth";
import { auth, db } from "../config/firebase";

// Firestore collection reference for inspectors
const INSPECTORS_COLLECTION = "inspectors";
const inspectorsCollectionRef = collection(db, INSPECTORS_COLLECTION);

/**
 * Create a new inspector account without logging out the admin
 * This uses a simplified approach that creates the inspector record first
 * @param {Object} inspectorData - Inspector data
 * @returns {Promise<Object>} Success status with inspector data
 */
export const createInspector = async (inspectorData) => {
  try {
    const { email, password, name, phone, company, employeeId } = inspectorData;

    // Store current admin user
    const currentUser = auth.currentUser;
    
    if (!currentUser) {
      throw new Error("Admin must be logged in to create inspectors");
    }

    const adminUid = currentUser.uid;
    const adminEmail = currentUser.email;

    console.log('🔐 Creating inspector account for:', email);
    console.log('👤 Admin session:', adminEmail);

    // Store admin session info in localStorage as backup
    localStorage.setItem('adminSessionBackup', JSON.stringify({
      uid: adminUid,
      email: adminEmail,
      timestamp: Date.now()
    }));

    // Create Firebase Auth account for inspector
    console.log('🔑 Creating Firebase Auth account...');
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const newUser = userCredential.user;

    // Update the new user's profile
    await updateProfile(newUser, {
      displayName: name
    });

    // Create inspector document in Firestore
    const inspectorDoc = {
      uid: newUser.uid,
      email: email,
      name: name,
      phone: phone || '',
      company: company || '',
      employeeId: employeeId || '',
      role: 'inspector',
      status: 'active',
      createdAt: serverTimestamp(),
      createdBy: adminUid,
      lastLogin: null,
      totalReports: 0,
      permissions: {
        canCreateReports: true,
        canViewOwnReports: true,
        canEditOwnReports: true,
        canDeleteOwnReports: false,
        canAccessAdminDashboard: false
      }
    };

    console.log('📝 Creating inspector document in Firestore...');
    const docRef = await addDoc(inspectorsCollectionRef, inspectorDoc);

    // CRITICAL: Sign out the inspector immediately and restore admin
    console.log('👋 Signing out inspector and restoring admin...');
    await signOut(auth);

    // Force immediate admin session restoration
    console.log('🔄 Forcing admin session restoration...');
    
    // Set a flag to prevent auth context from treating this as a logout
    window.adminSessionRestoring = true;
    
    // Trigger a custom event to notify the auth context
    window.dispatchEvent(new CustomEvent('forceAdminRestore', {
      detail: { adminUid, adminEmail }
    }));

    // Wait a moment for the auth state to settle
    await new Promise(resolve => setTimeout(resolve, 200));

    // Clean up backup
    localStorage.removeItem('adminSessionBackup');
    window.adminSessionRestoring = false;

    console.log('✅ Inspector created successfully!');

    return {
      success: true,
      message: "Inspector created successfully! You remain logged in as admin.",
      data: {
        id: docRef.id,
        uid: newUser.uid,
        email: email,
        name: name,
        phone: phone || '',
        company: company || '',
        employeeId: employeeId || '',
        role: 'inspector',
        status: 'active',
        createdAt: new Date().toISOString(),
        createdBy: adminUid,
        lastLogin: null,
        totalReports: 0,
        permissions: {
          canCreateReports: true,
          canViewOwnReports: true,
          canEditOwnReports: true,
          canDeleteOwnReports: false,
          canAccessAdminDashboard: false
        }
      }
    };

  } catch (error) {
    console.error("❌ Error creating inspector:", error);
    
    // Clean up on error
    localStorage.removeItem('adminSessionBackup');
    window.adminSessionRestoring = false;
    
    let message = "Failed to create inspector";
    if (error.code === "auth/email-already-in-use") {
      message = "An account with this email already exists";
    } else if (error.code === "auth/weak-password") {
      message = "Password should be at least 6 characters long";
    } else if (error.code === "auth/invalid-email") {
      message = "Invalid email address format";
    }

    return {
      success: false,
      error: error.code || "unknown-error",
      message: message
    };
  }
};

/**
 * Get all inspectors from Firestore
 * @returns {Promise<Object>} Inspectors data with success status
 */
export const getInspectorsFromFirestore = async () => {
  try {
    const q = query(inspectorsCollectionRef, orderBy("createdAt", "desc"));
    const querySnapshot = await getDocs(q);
    
    const inspectors = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      inspectors.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
        lastLogin: data.lastLogin?.toDate?.()?.toISOString() || data.lastLogin
      });
    });

    return {
      success: true,
      data: inspectors,
      message: "Inspectors loaded successfully"
    };
  } catch (error) {
    console.error("Error getting inspectors from Firestore:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to load inspectors from Firestore"
    };
  }
};

/**
 * Get a single inspector by ID
 * @param {string} inspectorId - Inspector document ID
 * @returns {Promise<Object>} Inspector data with success status
 */
export const getInspectorById = async (inspectorId) => {
  try {
    const docRef = doc(db, INSPECTORS_COLLECTION, inspectorId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      const data = docSnap.data();
      return {
        success: true,
        data: {
          id: docSnap.id,
          ...data,
          createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
          lastLogin: data.lastLogin?.toDate?.()?.toISOString() || data.lastLogin
        },
        message: "Inspector loaded successfully"
      };
    } else {
      return {
        success: false,
        error: "not-found",
        message: "Inspector not found"
      };
    }
  } catch (error) {
    console.error("Error getting inspector from Firestore:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to load inspector from Firestore"
    };
  }
};

/**
 * Update an existing inspector
 * @param {string} inspectorId - Inspector document ID
 * @param {Object} updateData - Data to update
 * @returns {Promise<Object>} Success status
 */
export const updateInspector = async (inspectorId, updateData) => {
  try {
    const docRef = doc(db, INSPECTORS_COLLECTION, inspectorId);
    const dataWithTimestamp = {
      ...updateData,
      updatedAt: serverTimestamp(),
      updatedBy: auth.currentUser?.uid || 'admin'
    };

    await updateDoc(docRef, dataWithTimestamp);
    
    return {
      success: true,
      message: "Inspector updated successfully"
    };
  } catch (error) {
    console.error("Error updating inspector in Firestore:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to update inspector in Firestore"
    };
  }
};

/**
 * Delete an inspector (soft delete by changing status)
 * @param {string} inspectorId - Inspector document ID
 * @returns {Promise<Object>} Success status
 */
export const deleteInspector = async (inspectorId) => {
  try {
    // Soft delete by updating status
    const result = await updateInspector(inspectorId, { 
      status: 'deleted',
      deletedAt: serverTimestamp(),
      deletedBy: auth.currentUser?.uid || 'admin'
    });
    
    if (result.success) {
      return {
        success: true,
        message: "Inspector deleted successfully"
      };
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error("Error deleting inspector:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to delete inspector"
    };
  }
};

/**
 * Activate/Deactivate an inspector
 * @param {string} inspectorId - Inspector document ID
 * @param {boolean} isActive - Whether to activate or deactivate
 * @returns {Promise<Object>} Success status
 */
export const toggleInspectorStatus = async (inspectorId, isActive) => {
  try {
    const status = isActive ? 'active' : 'inactive';
    const result = await updateInspector(inspectorId, { status });
    
    if (result.success) {
      return {
        success: true,
        message: `Inspector ${isActive ? 'activated' : 'deactivated'} successfully`
      };
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error("Error toggling inspector status:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to update inspector status"
    };
  }
};

/**
 * Reset inspector password
 * @param {string} inspectorId - Inspector document ID
 * @param {string} newPassword - New password
 * @returns {Promise<Object>} Success status
 */
export const resetInspectorPassword = async (inspectorId, newPassword) => {
  try {
    // Get inspector data
    const inspectorResult = await getInspectorById(inspectorId);
    if (!inspectorResult.success) {
      throw new Error("Inspector not found");
    }

    const inspector = inspectorResult.data;

    // Note: In production, you should use Firebase Admin SDK for this
    // For now, we'll update the password reset timestamp in Firestore
    const result = await updateInspector(inspectorId, {
      passwordResetAt: serverTimestamp(),
      passwordResetBy: auth.currentUser?.uid || 'admin',
      requiresPasswordChange: true
    });

    if (result.success) {
      return {
        success: true,
        message: "Password reset successfully. Inspector will be required to change password on next login.",
        data: {
          email: inspector.email,
          temporaryPassword: newPassword
        }
      };
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error("Error resetting inspector password:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to reset inspector password"
    };
  }
};

/**
 * Get inspector statistics
 * @returns {Promise<Object>} Statistics data
 */
export const getInspectorStatistics = async () => {
  try {
    const inspectorsResult = await getInspectorsFromFirestore();
    if (!inspectorsResult.success) {
      throw new Error(inspectorsResult.message);
    }

    const inspectors = inspectorsResult.data;
    const stats = {
      total: inspectors.length,
      active: inspectors.filter(i => i.status === 'active').length,
      inactive: inspectors.filter(i => i.status === 'inactive').length,
      deleted: inspectors.filter(i => i.status === 'deleted').length,
      recentlyCreated: inspectors.filter(i => {
        const createdDate = new Date(i.createdAt);
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        return createdDate > weekAgo;
      }).length,
      totalReports: inspectors.reduce((sum, i) => sum + (i.totalReports || 0), 0)
    };

    return {
      success: true,
      data: stats,
      message: "Statistics loaded successfully"
    };
  } catch (error) {
    console.error("Error getting inspector statistics:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to load statistics"
    };
  }
};

/**
 * Validate inspector credentials (for mobile app login)
 * @param {string} email - Inspector email
 * @param {string} password - Inspector password
 * @returns {Promise<Object>} Validation result
 */
export const validateInspectorCredentials = async (email, password) => {
  try {
    // This would be used by the mobile app to validate inspector login
    // and ensure they can't access admin dashboard
    
    // Get inspector by email
    const q = query(inspectorsCollectionRef, where("email", "==", email));
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      return {
        success: false,
        error: "inspector-not-found",
        message: "Inspector account not found"
      };
    }

    const inspectorDoc = querySnapshot.docs[0];
    const inspector = { id: inspectorDoc.id, ...inspectorDoc.data() };

    if (inspector.status !== 'active') {
      return {
        success: false,
        error: "account-disabled",
        message: "Inspector account is not active"
      };
    }

    // Update last login
    await updateInspector(inspector.id, {
      lastLogin: serverTimestamp()
    });

    return {
      success: true,
      message: "Inspector credentials validated",
      data: {
        id: inspector.id,
        uid: inspector.uid,
        email: inspector.email,
        name: inspector.name,
        role: inspector.role,
        permissions: inspector.permissions,
        canAccessAdminDashboard: false // Explicitly deny admin access
      }
    };

  } catch (error) {
    console.error("Error validating inspector credentials:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to validate credentials"
    };
  }
};