{"name": "inspection-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@tanstack/react-table": "^8.21.3", "clsx": "^2.1.1", "firebase": "^11.10.0", "html2canvas": "1.4.1", "jspdf": "2.5.1", "jspdf-autotable": "^5.0.2", "papaparse": "^5.5.3", "pdf-parse": "^1.1.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.58.1"}, "devDependencies": {"@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "vite": "^5.1.0"}}