import { doc, getDoc, setDoc, updateDoc } from "firebase/firestore";
import { updateProfile, updatePassword, reauthenticateWithCredential, EmailAuthProvider } from "firebase/auth";
import { auth, db } from "../config/firebase";

// Firestore document reference for settings
const SETTINGS_DOC_ID = "app-settings";
const settingsDocRef = doc(db, "settings", SETTINGS_DOC_ID);

/**
 * Get settings from Firestore
 * @returns {Promise<Object>} Settings data with success status
 */
export const getSettingsFromFirestore = async () => {
  try {
    const docSnap = await getDoc(settingsDocRef);
    
    if (docSnap.exists()) {
      const data = docSnap.data();
      return {
        success: true,
        data: {
          profile: data.profile || {
            name: '',
            email: '',
            phone: '',
            company: ''
          },
          delivery: data.delivery || {
            defaultEmail: '',
            sendToEmail: true,
            sendToCloud: false,
            cloudProvider: 'Google Drive',
            sendToInternal: false,
            internalFolderPath: '',
            emailTemplate: 'default',
            cloudFolderPath: '/Inspection Reports',
            autoSend: false
          },
          notifications: data.notifications || {
            emailNotifications: true,
            reportComplete: true,
            systemUpdates: false,
            weeklyDigest: false
          },
          appearance: data.appearance || {
            theme: 'light',
            language: 'en',
            timezone: 'UTC'
          },
          integrations: data.integrations || {
            googleDrive: {
              enabled: false,
              accessToken: null,
              refreshToken: null,
              folderId: null
            },
            dropbox: {
              enabled: false,
              accessToken: null,
              folderId: null
            },
            email: {
              smtpHost: '',
              smtpPort: 587,
              smtpUser: '',
              smtpPassword: '',
              fromEmail: '',
              fromName: 'Inspection Reports'
            }
          }
        },
        message: "Settings loaded successfully"
      };
    } else {
      // Return default settings if document doesn't exist
      return {
        success: true,
        data: {
          profile: {
            name: '',
            email: '',
            phone: '',
            company: ''
          },
          delivery: {
            defaultEmail: '',
            sendToEmail: true,
            sendToCloud: false,
            cloudProvider: 'Google Drive',
            sendToInternal: false,
            internalFolderPath: '',
            emailTemplate: 'default',
            cloudFolderPath: '/Inspection Reports',
            autoSend: false
          },
          notifications: {
            emailNotifications: true,
            reportComplete: true,
            systemUpdates: false,
            weeklyDigest: false
          },
          appearance: {
            theme: 'light',
            language: 'en',
            timezone: 'UTC'
          },
          integrations: {
            googleDrive: {
              enabled: false,
              accessToken: null,
              refreshToken: null,
              folderId: null
            },
            dropbox: {
              enabled: false,
              accessToken: null,
              folderId: null
            },
            email: {
              smtpHost: '',
              smtpPort: 587,
              smtpUser: '',
              smtpPassword: '',
              fromEmail: '',
              fromName: 'Inspection Reports'
            }
          }
        },
        message: "Default settings loaded"
      };
    }
  } catch (error) {
    console.error("Error getting settings from Firestore:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to load settings from Firestore"
    };
  }
};

/**
 * Save settings to Firestore
 * @param {Object} settingsData - Complete settings object
 * @returns {Promise<Object>} Success status
 */
export const saveSettingsToFirestore = async (settingsData) => {
  try {
    const dataWithTimestamp = {
      ...settingsData,
      updatedAt: new Date().toISOString(),
      updatedBy: auth.currentUser?.uid || 'unknown'
    };

    await setDoc(settingsDocRef, dataWithTimestamp, { merge: true });
    
    return {
      success: true,
      message: "Settings saved successfully"
    };
  } catch (error) {
    console.error("Error saving settings to Firestore:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to save settings to Firestore"
    };
  }
};

/**
 * Update user profile in Firebase Auth and Firestore
 * @param {Object} profileData - Profile data to update
 * @returns {Promise<Object>} Success status
 */
export const updateUserProfile = async (profileData) => {
  try {
    const user = auth.currentUser;
    if (!user) {
      throw new Error("No authenticated user");
    }

    // Update Firebase Auth profile
    await updateProfile(user, {
      displayName: profileData.name
    });

    // Update settings in Firestore
    const currentSettings = await getSettingsFromFirestore();
    if (currentSettings.success) {
      const updatedSettings = {
        ...currentSettings.data,
        profile: {
          ...currentSettings.data.profile,
          ...profileData,
          email: user.email // Keep email from auth
        }
      };
      
      await saveSettingsToFirestore(updatedSettings);
    }

    return {
      success: true,
      message: "Profile updated successfully"
    };
  } catch (error) {
    console.error("Error updating profile:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to update profile"
    };
  }
};

/**
 * Change user password
 * @param {string} currentPassword - Current password
 * @param {string} newPassword - New password
 * @returns {Promise<Object>} Success status
 */
export const changeUserPassword = async (currentPassword, newPassword) => {
  try {
    const user = auth.currentUser;
    if (!user || !user.email) {
      throw new Error("No authenticated user");
    }

    // Re-authenticate user before changing password
    const credential = EmailAuthProvider.credential(user.email, currentPassword);
    await reauthenticateWithCredential(user, credential);

    // Update password
    await updatePassword(user, newPassword);

    return {
      success: true,
      message: "Password changed successfully"
    };
  } catch (error) {
    console.error("Error changing password:", error);
    let message = "Failed to change password";
    
    if (error.code === "auth/wrong-password") {
      message = "Current password is incorrect";
    } else if (error.code === "auth/weak-password") {
      message = "New password is too weak";
    } else if (error.code === "auth/requires-recent-login") {
      message = "Please log out and log back in before changing password";
    }

    return {
      success: false,
      error: error.code || "unknown-error",
      message: message
    };
  }
};

/**
 * Update delivery settings
 * @param {Object} deliverySettings - Delivery settings to update
 * @returns {Promise<Object>} Success status
 */
export const updateDeliverySettings = async (deliverySettings) => {
  try {
    const currentSettings = await getSettingsFromFirestore();
    if (!currentSettings.success) {
      throw new Error(currentSettings.message);
    }

    const updatedSettings = {
      ...currentSettings.data,
      delivery: {
        ...currentSettings.data.delivery,
        ...deliverySettings
      }
    };

    return await saveSettingsToFirestore(updatedSettings);
  } catch (error) {
    console.error("Error updating delivery settings:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to update delivery settings"
    };
  }
};

/**
 * Update integration settings
 * @param {string} provider - Integration provider (googleDrive, dropbox, email)
 * @param {Object} integrationData - Integration data to update
 * @returns {Promise<Object>} Success status
 */
export const updateIntegrationSettings = async (provider, integrationData) => {
  try {
    const currentSettings = await getSettingsFromFirestore();
    if (!currentSettings.success) {
      throw new Error(currentSettings.message);
    }

    const updatedSettings = {
      ...currentSettings.data,
      integrations: {
        ...currentSettings.data.integrations,
        [provider]: {
          ...currentSettings.data.integrations[provider],
          ...integrationData
        }
      }
    };

    return await saveSettingsToFirestore(updatedSettings);
  } catch (error) {
    console.error("Error updating integration settings:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to update integration settings"
    };
  }
};
