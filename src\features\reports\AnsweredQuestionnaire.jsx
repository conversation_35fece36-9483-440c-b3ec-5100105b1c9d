import React from 'react';
import { useQuestions } from '../../hooks/useQuestions';

const getAnswerForQuestion = (question, report) => {
  // Handle both questionnaireData (old format) and questionnaire_responses (Firebase format)
  const responses = report.questionnaire_responses || report.questionnaireData || {};
  
  let answer;
  let otherAnswer = null;
  
  // Check if responses is in Firebase format (nested objects)
  if (responses[question.id] && typeof responses[question.id] === 'object' && responses[question.id].value !== undefined) {
    // Firebase format: { question_id: "...", question_text: "...", value: "...", ... }
    answer = responses[question.id].value;
    if (question.hasOther && responses[`${question.id}Other`]) {
      otherAnswer = responses[`${question.id}Other`].value || responses[`${question.id}Other`];
    }
  } else {
    // Old format: direct key-value pairs
    answer = responses[question.id];
    otherAnswer = question.hasOther ? responses[`${question.id}Other`] : null;
  }
  if (answer === undefined || answer === null || answer === '' || (Array.isArray(answer) && answer.length === 0)) {
    return <span className="text-gray-500 italic">No</span>;
  }
  let answerText = Array.isArray(answer) ? answer.join(', ') : String(answer);
  if (answer === 'Other' && otherAnswer) {
    answerText = `Other: ${otherAnswer}`;
  }
  return <span className="font-semibold">{answerText}</span>;
};

const AnsweredQuestionnaire = ({ report }) => {
  const { questions, sections } = useQuestions();

  // Check for questionnaire responses in either format
  const hasQuestionnaireData = report.questionnaire_responses || report.questionnaireData;
  
  if (!hasQuestionnaireData) {
    return (
      <div className="p-6 text-center text-gray-500">
        <p>This report does not have questionnaire responses.</p>
      </div>
    );
  }

  if (!questions.length || !sections.length) {
    return (
      <div className="p-6 text-center text-gray-500">
        Loading questionnaire data...
      </div>
    );
  }

  // Extract insured information from questionnaire responses
  const getInsuredInfo = (responses) => {
    if (!responses) return {};
    
    const info = {};
    
    // Helper function to extract value from response
    const extractValue = (response) => {
      if (typeof response === 'object' && response.value !== undefined) {
        return response.value;
      }
      return response;
    };
    
    // Search for insured information in questionnaire responses
    for (const [key, response] of Object.entries(responses)) {
      if (typeof response === 'object' && response.question_text && response.value) {
        const questionText = response.question_text.toLowerCase();
        
        // Insured Name
        if ((questionText.includes('insured') && questionText.includes('name')) ||
            (questionText.includes('property') && questionText.includes('owner')) ||
            (questionText.includes('client') && questionText.includes('name'))) {
          info.insuredName = response.value;
        }
        
        // Address
        if ((questionText.includes('address') && questionText.includes('street')) ||
            (questionText.includes('property') && questionText.includes('address')) ||
            (questionText.includes('insured') && questionText.includes('address'))) {
          info.address = response.value;
        }
        
        // State
        if (questionText.includes('state') && !questionText.includes('estate')) {
          info.state = response.value;
        }
        
        // Zip Code
        if (questionText.includes('zip') || questionText.includes('postal')) {
          info.zipCode = response.value;
        }
        
        // Policy Number
        if (questionText.includes('policy') && questionText.includes('number')) {
          info.policyNumber = response.value;
        }
      }
    }
    
    // Also check direct field names
    const directFields = {
      insuredName: ['insuredName', 'insured_name', 'clientName', 'client_name', 'propertyOwner', 'property_owner'],
      address: ['address', 'streetAddress', 'street_address', 'propertyAddress', 'property_address', 'insuredAddress', 'insured_address'],
      state: ['state', 'insuredState', 'insured_state'],
      zipCode: ['zipCode', 'zip_code', 'postalCode', 'postal_code', 'insuredZipCode', 'insured_zip_code'],
      policyNumber: ['policyNumber', 'policy_number', 'policyNo', 'policy_no']
    };
    
    for (const [infoKey, fieldNames] of Object.entries(directFields)) {
      if (!info[infoKey]) {
        for (const fieldName of fieldNames) {
          if (responses[fieldName]) {
            info[infoKey] = extractValue(responses[fieldName]);
            break;
          }
        }
      }
    }
    
    return info;
  };

  const insuredInfo = getInsuredInfo(hasQuestionnaireData);
  
  // Insured/Instructions Info with real Firebase data
  const insuredInfoSection = (
    <div className="mb-4">
      <div className="flex flex-col items-start gap-1 text-sm">
        <div className="flex flex-row gap-2">
          <span className="font-bold">Insured:</span>
          <span>{insuredInfo.insuredName || 'Not specified'}</span>
        </div>
        <div className="flex flex-row gap-2">
          <span className="font-bold">Address:</span>
          <span>{insuredInfo.address || 'Not specified'}</span>
        </div>
        <div className="flex flex-row gap-2">
          <span className="font-bold">State:</span>
          <span>{insuredInfo.state || 'Not specified'}</span>
        </div>
        <div className="flex flex-row gap-2">
          <span className="font-bold">Zip Code:</span>
          <span>{insuredInfo.zipCode || 'Not specified'}</span>
        </div>
        <div className="flex flex-row gap-2">
          <span className="font-bold">Policy Number:</span>
          <span>{insuredInfo.policyNumber || 'Not specified'}</span>
        </div>
        <div className="flex flex-row gap-2">
          <span className="font-bold">Inspection Date:</span>
          <span>{report.date ? report.date.split('T')[0] : 'Not specified'}</span>
        </div>
        <div className="flex flex-row gap-2">
          <span className="font-bold">Inspector:</span>
          <span>
            {report.inspectorName ? (
              <>
                {report.inspectorName}
                <span className="text-xs text-gray-500 ml-1">(ID: {report.inspectorId})</span>
              </>
            ) : (
              report.inspectorId || 'Not specified'
            )}
          </span>
        </div>
      </div>
      <div className="mt-4 text-sm">
        {(() => {
          // Extract custom report introduction/summary from Firebase
          const getReportIntroduction = (responses) => {
            if (!responses) return null;
            
            // Look for custom introduction/summary fields
            const introFields = [
              'reportIntroduction', 'report_introduction', 'introduction',
              'reportSummary', 'report_summary', 'summary', 'coverLetter', 'cover_letter',
              'insuranceMessage', 'insurance_message', 'clientMessage', 'client_message'
            ];
            
            // First, try direct field names
            for (const field of introFields) {
              if (responses[field]) {
                const value = typeof responses[field] === 'object' && responses[field].value 
                  ? responses[field].value 
                  : responses[field];
                if (value && typeof value === 'string' && value.trim().length > 0) {
                  return value;
                }
              }
            }
            
            // Second, search through questionnaire responses for introduction-related questions
            for (const [key, response] of Object.entries(responses)) {
              if (typeof response === 'object' && response.question_text && response.value) {
                const questionText = response.question_text.toLowerCase();
                if ((questionText.includes('introduction') || questionText.includes('summary') || 
                     questionText.includes('cover') || questionText.includes('message') ||
                     questionText.includes('letter') || questionText.includes('description')) &&
                    response.value && response.value.trim().length > 20) { // Ensure it's substantial text
                  return response.value;
                }
              }
            }
            
            return null;
          };
          
          const customIntroduction = getReportIntroduction(hasQuestionnaireData);
          
          if (customIntroduction) {
            // Use custom introduction from Firebase
            return customIntroduction;
          } else {
            // Use dynamic template with Firebase data
            const insuranceCompany = (() => {
              // Try to extract insurance company name from responses
              if (hasQuestionnaireData) {
                for (const [key, response] of Object.entries(hasQuestionnaireData)) {
                  if (typeof response === 'object' && response.question_text && response.value) {
                    const questionText = response.question_text.toLowerCase();
                    if (questionText.includes('insurance') && questionText.includes('company')) {
                      return response.value;
                    }
                  }
                }
              }
              return 'Mercury Insurance'; // Default fallback
            })();
            
            const inspectionCompany = (() => {
              // Try to extract inspection company name from responses
              if (hasQuestionnaireData) {
                for (const [key, response] of Object.entries(hasQuestionnaireData)) {
                  if (typeof response === 'object' && response.question_text && response.value) {
                    const questionText = response.question_text.toLowerCase();
                    if ((questionText.includes('inspection') || questionText.includes('company')) && 
                        questionText.includes('name')) {
                      return response.value;
                    }
                  }
                }
              }
              return 'Safe Harbor'; // Default fallback
            })();
            
            return `Dear ${insuranceCompany},\n\nPlease see the below underwriting inspection report outlining the overview of the property at ${insuredInfo.address || 'Property Address Not Specified'}, ${insuredInfo.state || 'State Not Specified'} ${insuredInfo.zipCode || 'Zip Not Specified'}. The assignment was submitted to ${inspectionCompany} with a request to complete an exterior underwriting inspection. Thank you for the opportunity to provide you with this report.`;
          }
        })()}
      </div>
    </div>
  );

  // Map question id to question object for fast lookup
  const questionMap = Object.fromEntries(questions.map(q => [q.id, q]));

  return (
    <div className="text-xs sm:text-sm flex flex-col gap-6">
      {insuredInfoSection}
      {sections.map(section => (
        <div key={section.title}>
          <div className="bg-blue-900 text-white font-bold text-center py-1 rounded-t-md mb-0.5 uppercase tracking-wide text-sm">{section.title}</div>
          <table className="w-full border border-gray-300 text-left mb-2">
            <tbody>
              {section.questions.reduce((rows, qid, idx, arr) => {
                if (idx % 2 === 0) {
                  rows.push(arr.slice(idx, idx + 2));
                }
                return rows;
              }, []).map((pair, i) => (
                <tr key={i} className="even:bg-gray-100">
                  {pair.map(qid => (
                    <React.Fragment key={qid}>
                      <td className="font-semibold border px-2 py-1 w-1/4 align-top">{questionMap[qid]?.label || ''}</td>
                      <td className="border px-2 py-1 w-1/4 align-top">{questionMap[qid] ? getAnswerForQuestion(questionMap[qid], report) : ''}</td>
                    </React.Fragment>
                  ))}
                  {pair.length < 2 && <React.Fragment key="empty"><td className="border px-2 py-1 w-1/4"></td><td className="border px-2 py-1 w-1/4"></td></React.Fragment>}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ))}
      {/* Summary Section */}
      <div>
        <div className="bg-blue-900 text-white font-bold text-center py-1 rounded-t-md mb-0.5 uppercase tracking-wide text-sm">Summary</div>
        <div className="border border-gray-300 p-2 bg-gray-50 min-h-[40px]">
          {(report.questionnaire_responses?.summary || report.questionnaireData?.summary) || 
           'The property is generally in good condition with no evidence of roof damage, exterior elevation damage was noted to right elevation window along with tipping hazard on front walkway. The property has been inspected, and the surrounding area is free of debris. A six-foot wooden fence enclosing the property\'s rear is in good condition. No additional recommendations are being made at this time.'}
        </div>
      </div>
    </div>
  );
};

export default AnsweredQuestionnaire; 