import React from 'react';

const SECTIONS = [
  {
    title: 'Overall Risk Information',
    questions: [
      'neighborhood', 'rentalProperty', 'areaEconomy', 'businessOnSite', 'gatedCommunity', 'seasonalHome',
      'propertyVacant', 'historicProperty', 'nearestBodyOfWater', 'nearestDwelling',
    ],
  },
  {
    title: 'Overall Elevation Condition',
    questions: [
      'dwellingType', 'sidingDamage', 'yearBuilt', 'peelingPaint', 'typeOfFoundation', 'mildewMoss',
      'primaryConstruction', 'windowDamage', 'numberOfStories', 'foundationCracks', 'livingArea', 'wallCracks',
      'lotSize', 'chimneyDamage', 'siding', 'waterDamage', 'hvac', 'underRenovation', 'numberOfHVACSystems',
      'mainBreakerPanel', 'hvacSerialNumbers', 'waterSpicketDamage', 'guttersAndDownspout', 'doorDamage', 'fuelTank',
      'odorDamage',
    ],
  },
  {
    title: 'Overall Roof Condition',
    questions: [
      'roofMaterials', 'missingShinglesTiles', 'roofCovering', 'priorRepairs', 'ageOfRoof', 'curlingShingles',
      'shapeOfRoof', 'algaeMoss', 'treeLimbsOnRoof', 'tarpOnRoof', 'debrisOnRoof', 'brokenOrCrackedTiles',
      'solarPanel', 'satelliteDish', 'exposedFelt', 'unevenDecking',
    ],
  },
  {
    title: 'Garage/Outbuilding Overall Condition',
    questions: [
      'garageType', 'garageCondition', 'carportOrAwning', 'carportConstruction', 'outbuilding', 'outbuildingType', 'fenceDetails', 'fenceCondition',
    ],
  },
  {
    title: 'Dwelling Hazards',
    questions: [
      'boardedDoorsWindows', 'hurricaneShutters', 'overgrownVegetation', 'treeBranch', 'abandonedVehicles', 'chimneyThroughRoof',
      'missingDamagedSteps', 'fireplacePitOutside', 'missingDamageRailing', 'securityBars', 'sidingDamageHazard', 'fasciaSoffitDamage',
    ],
  },
  {
    title: 'Possible Hazards',
    questions: [
      'swimmingPool', 'dog', 'divingBoardOrSlide', 'dogType', 'poolFenced', 'dogSign', 'trampoline', 'skateboardOrBikeRamp',
      'swingSet', 'treeHouse', 'basketballGoal', 'debrisInYard',
    ],
  },
];

const getAnswerForQuestion = (question, report) => {
  const answer = report.questionnaireData[question.id];
  const otherAnswer = question.hasOther ? report.questionnaireData[`${question.id}Other`] : null;
  if (answer === undefined || answer === null || answer === '' || (Array.isArray(answer) && answer.length === 0)) {
    return <span className="text-gray-500 italic">No</span>;
  }
  let answerText = Array.isArray(answer) ? answer.join(', ') : String(answer);
  if (answer === 'Other' && otherAnswer) {
    answerText = `Other: ${otherAnswer}`;
  }
  return <span className="font-semibold">{answerText}</span>;
};

const AnsweredQuestionnaire = ({ report, questions }) => {
  if (report.status !== 'Completed' || !report.questionnaireData) {
    return (
      <div className="p-6 text-center text-gray-500">
        This report does not have a completed questionnaire.
      </div>
    );
  }

  // Insured/Instructions Info (now inside AnsweredQuestionnaire)
  const insuredInfo = (
    <div className="mb-4">
      <div className="flex flex-col items-start gap-1 text-sm">
        <div className="flex flex-row gap-2"><span className="font-bold">Insured:</span><span>{report.insuredName || 'John Smith'}</span></div>
        <div className="flex flex-row gap-2"><span className="font-bold">Address:</span><span>{report.insuredStreetAddress || '6759 Biltmore Ct. Mobile'}</span></div>
        <div className="flex flex-row gap-2"><span className="font-bold">State</span><span>{report.insuredState || 'Alabama'}</span></div>
        <div className="flex flex-row gap-2"><span className="font-bold">Zip Code</span><span>{report.insuredZipCode || '36693'}</span></div>
        <div className="flex flex-row gap-2"><span className="font-bold">Policy Number:</span><span>{report.policyNumber || '2025TEST123'}</span></div>
        <div className="flex flex-row gap-2"><span className="font-bold">Inspection Date:</span><span>{report.date ? report.date.split(' ')[0] : '5/7/2025'}</span></div>
        <div className="flex flex-row gap-2"><span className="font-bold">Inspectors Name:</span><span>{report.userName || 'John Doe'}</span></div>
      </div>
      <div className="mt-4 text-sm">
        Dear Mercury Insurance,<br />
        Please see the below underwriting inspection report outlining the overview of the property at {report.insuredStreetAddress || '6759 Biltmore Ct. Mobile'}, {report.insuredState || 'AL'} {report.insuredZipCode || '36693'}. The assignment was submitted to Safe Harbor with a request to complete an exterior underwriting inspection. Thank you for the opportunity to provide you with this report
      </div>
    </div>
  );

  // Map question id to question object for fast lookup
  const questionMap = Object.fromEntries(questions.map(q => [q.id, q]));

  return (
    <div className="text-xs sm:text-sm flex flex-col gap-6">
      {insuredInfo}
      {SECTIONS.map(section => (
        <div key={section.title}>
          <div className="bg-blue-900 text-white font-bold text-center py-1 rounded-t-md mb-0.5 uppercase tracking-wide text-sm">{section.title}</div>
          <table className="w-full border border-gray-300 text-left mb-2">
            <tbody>
              {section.questions.reduce((rows, qid, idx, arr) => {
                if (idx % 2 === 0) {
                  rows.push(arr.slice(idx, idx + 2));
                }
                return rows;
              }, []).map((pair, i) => (
                <tr key={i} className="even:bg-gray-100">
                  {pair.map(qid => (
                    <React.Fragment key={qid}>
                      <td className="font-semibold border px-2 py-1 w-1/4 align-top">{questionMap[qid]?.label || ''}</td>
                      <td className="border px-2 py-1 w-1/4 align-top">{questionMap[qid] ? getAnswerForQuestion(questionMap[qid], report) : ''}</td>
                    </React.Fragment>
                  ))}
                  {pair.length < 2 && <React.Fragment key="empty"><td className="border px-2 py-1 w-1/4"></td><td className="border px-2 py-1 w-1/4"></td></React.Fragment>}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ))}
      {/* Summary Section */}
      <div>
        <div className="bg-blue-900 text-white font-bold text-center py-1 rounded-t-md mb-0.5 uppercase tracking-wide text-sm">Summary</div>
        <div className="border border-gray-300 p-2 bg-gray-50 min-h-[40px]">{report.questionnaireData.summary || 'The property is generally in good condition with no evidence of roof damage, exterior elevation damage was noted to right elevation window along with tipping hazard on front walkway. The property has been inspected, and the surrounding area is free of debris. A six-foot wooden fence enclosing the property\'s rear is in good condition. No additional recommendations are being made at this time.'}</div>
      </div>
    </div>
  );
};

export default AnsweredQuestionnaire; 