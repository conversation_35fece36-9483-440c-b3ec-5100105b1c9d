import {
  collection,
  doc,
  getDocs,
  getDoc,
  updateDoc,
  query,
  orderBy,
  where
} from "firebase/firestore";
import { db } from "../config/firebase";

// Firestore collection reference for reports
const REPORTS_COLLECTION = "inspection-reports";
const reportsCollectionRef = collection(db, REPORTS_COLLECTION);

/**
 * Get all reports from Firestore
 * @returns {Promise<Object>} Reports data with success status
 */
export const getReportsFromFirestore = async () => {
  try {
    const q = query(reportsCollectionRef, orderBy("createdAt", "desc"));
    const querySnapshot = await getDocs(q);
    
    const reports = [];
    querySnapshot.forEach((doc) => {
      reports.push({
        id: doc.id,
        ...doc.data()
      });
    });

    return {
      success: true,
      data: reports,
      message: "Reports loaded successfully"
    };
  } catch (error) {
    console.error("Error getting reports from Firestore:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to load reports from Firestore"
    };
  }
};

/**
 * Get a single report by ID from Firestore
 * @param {string} reportId - Report ID
 * @returns {Promise<Object>} Report data with success status
 */
export const getReportFromFirestore = async (reportId) => {
  try {
    const docRef = doc(db, REPORTS_COLLECTION, reportId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return {
        success: true,
        data: {
          id: docSnap.id,
          ...docSnap.data()
        },
        message: "Report loaded successfully"
      };
    } else {
      return {
        success: false,
        error: "not-found",
        message: "Report not found"
      };
    }
  } catch (error) {
    console.error("Error getting report from Firestore:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to load report from Firestore"
    };
  }
};



/**
 * Update an existing report in Firestore
 * @param {string} reportId - Report ID
 * @param {Object} updateData - Data to update
 * @returns {Promise<Object>} Success status
 */
export const updateReportInFirestore = async (reportId, updateData) => {
  try {
    const docRef = doc(db, REPORTS_COLLECTION, reportId);
    const dataWithTimestamp = {
      ...updateData,
      updatedAt: serverTimestamp()
    };

    await updateDoc(docRef, dataWithTimestamp);
    
    return {
      success: true,
      message: "Report updated successfully"
    };
  } catch (error) {
    console.error("Error updating report in Firestore:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to update report in Firestore"
    };
  }
};



/**
 * Get reports by status from Firestore
 * @param {string} status - Report status to filter by
 * @returns {Promise<Object>} Filtered reports data
 */
export const getReportsByStatus = async (status) => {
  try {
    const q = query(
      reportsCollectionRef, 
      where("status", "==", status),
      orderBy("createdAt", "desc")
    );
    const querySnapshot = await getDocs(q);
    
    const reports = [];
    querySnapshot.forEach((doc) => {
      reports.push({
        id: doc.id,
        ...doc.data()
      });
    });

    return {
      success: true,
      data: reports,
      message: `Reports with status '${status}' loaded successfully`
    };
  } catch (error) {
    console.error("Error getting reports by status from Firestore:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to load reports by status from Firestore"
    };
  }
};

/**
 * Archive a report (update status to 'Archived')
 * @param {string} reportId - Report ID
 * @returns {Promise<Object>} Success status
 */
export const archiveReport = async (reportId) => {
  return await updateReportInFirestore(reportId, { status: 'Archived' });
};




