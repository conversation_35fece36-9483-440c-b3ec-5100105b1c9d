import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  orderBy, 
  where,
  serverTimestamp 
} from "firebase/firestore";
import { db } from "../config/firebase";

// Firestore collection reference for reports
const REPORTS_COLLECTION = "inspection-reports";
const reportsCollectionRef = collection(db, REPORTS_COLLECTION);

/**
 * Get all reports from Firestore
 * @returns {Promise<Object>} Reports data with success status
 */
export const getReportsFromFirestore = async () => {
  try {
    const q = query(reportsCollectionRef, orderBy("createdAt", "desc"));
    const querySnapshot = await getDocs(q);
    
    const reports = [];
    querySnapshot.forEach((doc) => {
      reports.push({
        id: doc.id,
        ...doc.data()
      });
    });

    return {
      success: true,
      data: reports,
      message: "Reports loaded successfully"
    };
  } catch (error) {
    console.error("Error getting reports from Firestore:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to load reports from Firestore"
    };
  }
};

/**
 * Get a single report by ID from Firestore
 * @param {string} reportId - Report ID
 * @returns {Promise<Object>} Report data with success status
 */
export const getReportFromFirestore = async (reportId) => {
  try {
    const docRef = doc(db, REPORTS_COLLECTION, reportId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return {
        success: true,
        data: {
          id: docSnap.id,
          ...docSnap.data()
        },
        message: "Report loaded successfully"
      };
    } else {
      return {
        success: false,
        error: "not-found",
        message: "Report not found"
      };
    }
  } catch (error) {
    console.error("Error getting report from Firestore:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to load report from Firestore"
    };
  }
};

/**
 * Save a new report to Firestore
 * @param {Object} reportData - Report data object
 * @returns {Promise<Object>} Success status with report ID
 */
export const saveReportToFirestore = async (reportData) => {
  try {
    const reportWithTimestamp = {
      ...reportData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    const docRef = await addDoc(reportsCollectionRef, reportWithTimestamp);
    
    return {
      success: true,
      data: { id: docRef.id },
      message: "Report saved successfully"
    };
  } catch (error) {
    console.error("Error saving report to Firestore:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to save report to Firestore"
    };
  }
};

/**
 * Update an existing report in Firestore
 * @param {string} reportId - Report ID
 * @param {Object} updateData - Data to update
 * @returns {Promise<Object>} Success status
 */
export const updateReportInFirestore = async (reportId, updateData) => {
  try {
    const docRef = doc(db, REPORTS_COLLECTION, reportId);
    const dataWithTimestamp = {
      ...updateData,
      updatedAt: serverTimestamp()
    };

    await updateDoc(docRef, dataWithTimestamp);
    
    return {
      success: true,
      message: "Report updated successfully"
    };
  } catch (error) {
    console.error("Error updating report in Firestore:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to update report in Firestore"
    };
  }
};

/**
 * Delete a report from Firestore
 * @param {string} reportId - Report ID
 * @returns {Promise<Object>} Success status
 */
export const deleteReportFromFirestore = async (reportId) => {
  try {
    const docRef = doc(db, REPORTS_COLLECTION, reportId);
    await deleteDoc(docRef);
    
    return {
      success: true,
      message: "Report deleted successfully"
    };
  } catch (error) {
    console.error("Error deleting report from Firestore:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to delete report from Firestore"
    };
  }
};

/**
 * Get reports by status from Firestore
 * @param {string} status - Report status to filter by
 * @returns {Promise<Object>} Filtered reports data
 */
export const getReportsByStatus = async (status) => {
  try {
    const q = query(
      reportsCollectionRef, 
      where("status", "==", status),
      orderBy("createdAt", "desc")
    );
    const querySnapshot = await getDocs(q);
    
    const reports = [];
    querySnapshot.forEach((doc) => {
      reports.push({
        id: doc.id,
        ...doc.data()
      });
    });

    return {
      success: true,
      data: reports,
      message: `Reports with status '${status}' loaded successfully`
    };
  } catch (error) {
    console.error("Error getting reports by status from Firestore:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to load reports by status from Firestore"
    };
  }
};

/**
 * Archive a report (update status to 'Archived')
 * @param {string} reportId - Report ID
 * @returns {Promise<Object>} Success status
 */
export const archiveReport = async (reportId) => {
  return await updateReportInFirestore(reportId, { status: 'Archived' });
};

/**
 * Migration function to populate Firebase with sample reports data
 * This function creates sample reports for testing
 * @returns {Promise<Object>} Migration result
 */
export const migrateReportsToFirebase = async () => {
  try {
    console.log('🚀 Starting reports migration to Firebase...');

    // Sample reports data (you can replace this with actual data from mobile app)
    const sampleReports = [
      {
        reportName: 'Property Condition Report',
        userName: 'Arif Jan',
        property: '123 Main St',
        date: '2025-01-14 10:30',
        status: 'Completed',
        photos: [
          'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=400&q=80',
          'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=400&q=80',
          'https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=400&q=80'
        ],
        questionnaireData: {
          inspectorName: 'Arif Jan',
          droneNumber: 'AL 1',
          policyNumber: 'POLICY-001',
          insuredName: 'John Smith',
          insuredStreetAddress: '123 Main St',
          insuredState: 'California',
          insuredZipCode: '90210',
          dateOfInspection: '2025-01-14',
          neighborhood: 'Suburban',
          areaEconomy: 'Stable',
          gatedCommunity: 'Yes',
          propertyVacant: 'No',
          summary: 'The property is in good condition with no major issues identified during the inspection.'
        }
      },
      {
        reportName: 'Initial Site Assessment',
        userName: 'Hanzallah',
        property: '456 Oak Ave',
        date: '2025-01-14 14:15',
        status: 'Pending',
        photos: [],
        questionnaireData: {}
      }
    ];

    console.log(`📊 Migrating ${sampleReports.length} sample reports`);

    // Save each report to Firebase
    const results = [];
    for (const reportData of sampleReports) {
      const result = await saveReportToFirestore(reportData);
      results.push(result);
    }

    const successCount = results.filter(r => r.success).length;

    console.log(`✅ Migration completed! ${successCount}/${sampleReports.length} reports migrated successfully`);

    return {
      success: true,
      message: "Reports migration completed successfully",
      data: {
        totalReports: sampleReports.length,
        successfulMigrations: successCount,
        results: results
      }
    };
  } catch (error) {
    console.error("❌ Error during reports migration:", error);
    return {
      success: false,
      error: error.code || "migration-error",
      message: "Failed to migrate reports to Firebase"
    };
  }
};


