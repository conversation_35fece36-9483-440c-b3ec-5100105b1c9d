import {
  collection,
  doc,
  getDocs,
  getDoc,
  updateDoc,
  query,
  orderBy,
  where,
  serverTimestamp
} from "firebase/firestore";
import { db } from "../config/firebase";

// Firestore collection reference for reports
const REPORTS_COLLECTION = "inspection_reports";
const reportsCollectionRef = collection(db, REPORTS_COLLECTION);

/**
 * Get all reports from Firestore with flexible data handling
 * @returns {Promise<Object>} Reports data with success status
 */
export const getReportsFromFirestore = async () => {
  try {
    // Query reports ordered by created_at timestamp
    const q = query(reportsCollectionRef, orderBy("created_at", "desc"));
    const querySnapshot = await getDocs(q);
    
    const reports = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      
      // Extract inspector name from questionnaire responses
      const getInspectorName = (responses) => {
        if (!responses) return null;
        
        // Look for inspector name in common field names and question IDs
        const nameFields = [
          'inspectorName', 'inspector_name', 'inspectorsName', 'name',
          'inspectorFullName', 'inspector_full_name', 'fullName', 'full_name'
        ];
        
        // First, try direct field names
        for (const field of nameFields) {
          if (responses[field]) {
            // Handle both direct values and nested objects
            if (typeof responses[field] === 'object' && responses[field].value) {
              return responses[field].value;
            } else if (typeof responses[field] === 'string') {
              return responses[field];
            }
          }
        }
        
        // Second, search through all responses for inspector-related questions
        for (const [key, response] of Object.entries(responses)) {
          if (typeof response === 'object' && response.question_text && response.value) {
            const questionText = response.question_text.toLowerCase();
            // Look for questions that might contain inspector name
            if (questionText.includes('inspector') && 
                (questionText.includes('name') || questionText.includes('full'))) {
              return response.value;
            }
          }
        }
        
        return null;
      };

      const inspectorName = getInspectorName(data.questionnaire_responses);
      const inspectorId = data.inspector_id || 'Unknown ID';

      // Normalize the data structure based on Firebase structure
      const normalizedReport = {
        id: doc.id,
        ...data,
        // Handle date from created_at timestamp
        date: data.created_at?.toDate?.()?.toISOString() || new Date().toISOString(),
        // Ensure consistent status
        status: data.status || 'Active',
        // Create report name from document ID
        reportName: data.reportName || `Inspection Report ${doc.id.substring(0, 8)}`,
        // Use inspector name if available, otherwise inspector ID
        userName: inspectorName || inspectorId,
        // Store both inspector name and ID for display
        inspectorName: inspectorName,
        inspectorId: inspectorId,
        // Include questionnaire responses for display
        qa: data.questionnaire_responses || [],
        // Include images if available
        images: data.images || {},
        // Add property info if available
        property: data.property || 'Property Inspection'
      };
      
      reports.push(normalizedReport);
    });

    return {
      success: true,
      data: reports,
      message: `Reports loaded successfully (${reports.length} found)`
    };
  } catch (error) {
    console.error("Error getting reports from Firestore:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to load reports from Firestore"
    };
  }
};

/**
 * Get a single report by ID from Firestore
 * @param {string} reportId - Report ID
 * @returns {Promise<Object>} Report data with success status
 */
export const getReportFromFirestore = async (reportId) => {
  try {
    const docRef = doc(db, REPORTS_COLLECTION, reportId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return {
        success: true,
        data: {
          id: docSnap.id,
          ...docSnap.data()
        },
        message: "Report loaded successfully"
      };
    } else {
      return {
        success: false,
        error: "not-found",
        message: "Report not found"
      };
    }
  } catch (error) {
    console.error("Error getting report from Firestore:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to load report from Firestore"
    };
  }
};



/**
 * Update an existing report in Firestore
 * @param {string} reportId - Report ID
 * @param {Object} updateData - Data to update
 * @returns {Promise<Object>} Success status
 */
export const updateReportInFirestore = async (reportId, updateData) => {
  try {
    const docRef = doc(db, REPORTS_COLLECTION, reportId);
    const dataWithTimestamp = {
      ...updateData,
      updatedAt: serverTimestamp()
    };

    await updateDoc(docRef, dataWithTimestamp);
    
    return {
      success: true,
      message: "Report updated successfully"
    };
  } catch (error) {
    console.error("Error updating report in Firestore:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to update report in Firestore"
    };
  }
};



/**
 * Get reports by status from Firestore
 * @param {string} status - Report status to filter by
 * @returns {Promise<Object>} Filtered reports data
 */
export const getReportsByStatus = async (status) => {
  try {
    const q = query(
      reportsCollectionRef, 
      where("status", "==", status),
      orderBy("created_at", "desc")
    );
    const querySnapshot = await getDocs(q);
    
    const reports = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      
      // Apply same normalization as getReportsFromFirestore
      const getInspectorName = (responses) => {
        if (!responses) return null;
        
        const nameFields = [
          'inspectorName', 'inspector_name', 'inspectorsName', 'name',
          'inspectorFullName', 'inspector_full_name', 'fullName', 'full_name'
        ];
        
        for (const field of nameFields) {
          if (responses[field]) {
            if (typeof responses[field] === 'object' && responses[field].value) {
              return responses[field].value;
            } else if (typeof responses[field] === 'string') {
              return responses[field];
            }
          }
        }
        
        for (const [, response] of Object.entries(responses)) {
          if (typeof response === 'object' && response.question_text && response.value) {
            const questionText = response.question_text.toLowerCase();
            if (questionText.includes('inspector') && 
                (questionText.includes('name') || questionText.includes('full'))) {
              return response.value;
            }
          }
        }
        
        return null;
      };

      const inspectorName = getInspectorName(data.questionnaire_responses);
      const inspectorId = data.inspector_id || 'Unknown ID';

      const normalizedReport = {
        id: doc.id,
        ...data,
        date: data.created_at?.toDate?.()?.toISOString() || new Date().toISOString(),
        status: data.status || 'Active',
        reportName: data.reportName || `Inspection Report ${doc.id.substring(0, 8)}`,
        userName: inspectorName || inspectorId,
        inspectorName: inspectorName,
        inspectorId: inspectorId,
        qa: data.questionnaire_responses || [],
        images: data.images || {},
        property: data.property || 'Property Inspection'
      };
      
      reports.push(normalizedReport);
    });

    return {
      success: true,
      data: reports,
      message: `Reports with status '${status}' loaded successfully (${reports.length} found)`
    };
  } catch (error) {
    console.error("Error getting reports by status from Firestore:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to load reports by status from Firestore"
    };
  }
};

/**
 * Archive a report (update status to 'Archived')
 * @param {string} reportId - Report ID
 * @returns {Promise<Object>} Success status
 */
export const archiveReport = async (reportId) => {
  try {
    const result = await updateReportInFirestore(reportId, { 
      status: 'Archived',
      archivedAt: serverTimestamp()
    });
    
    if (result.success) {
      return {
        success: true,
        message: "Report archived successfully"
      };
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error("Error archiving report:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to archive report"
    };
  }
};

/**
 * Unarchive a report (update status back to 'Active')
 * @param {string} reportId - Report ID
 * @returns {Promise<Object>} Success status
 */
export const unarchiveReport = async (reportId) => {
  try {
    const result = await updateReportInFirestore(reportId, { 
      status: 'Active',
      unarchivedAt: serverTimestamp()
    });
    
    if (result.success) {
      return {
        success: true,
        message: "Report unarchived successfully"
      };
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error("Error unarchiving report:", error);
    return {
      success: false,
      error: error.code || "unknown-error",
      message: "Failed to unarchive report"
    };
  }
};

/**
 * Get archived reports from Firestore
 * @returns {Promise<Object>} Archived reports data
 */
export const getArchivedReports = async () => {
  return await getReportsByStatus('Archived');
};




