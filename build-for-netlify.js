#!/usr/bin/env node

/**
 * Build script for Netlify deployment
 * This script ensures everything is properly built and configured
 */

import { execSync } from 'child_process';
import { existsSync, readFileSync, writeFileSync } from 'fs';
import { join } from 'path';

console.log('🚀 Starting Netlify build process...\n');

try {
  // Step 1: Clean previous builds
  console.log('🧹 Cleaning previous builds...');
  if (existsSync('dist')) {
    execSync('rm -rf dist', { stdio: 'inherit' });
  }
  if (existsSync('node_modules/.vite')) {
    execSync('rm -rf node_modules/.vite', { stdio: 'inherit' });
  }
  console.log('✅ Cleanup completed\n');

  // Step 2: Install dependencies (if needed)
  console.log('📦 Checking dependencies...');
  if (!existsSync('node_modules')) {
    console.log('Installing dependencies...');
    execSync('npm install', { stdio: 'inherit' });
  }
  console.log('✅ Dependencies ready\n');

  // Step 3: Build the project
  console.log('🔨 Building the project...');
  execSync('npm run build', { stdio: 'inherit' });
  console.log('✅ Build completed\n');

  // Step 4: Verify build output
  console.log('🔍 Verifying build output...');
  
  const distPath = 'dist';
  if (!existsSync(distPath)) {
    throw new Error('Build failed: dist directory not found');
  }

  const indexPath = join(distPath, 'index.html');
  if (!existsSync(indexPath)) {
    throw new Error('Build failed: index.html not found in dist directory');
  }

  // Check if _redirects file exists in dist
  const redirectsPath = join(distPath, '_redirects');
  if (!existsSync(redirectsPath)) {
    console.log('📝 Creating _redirects file in dist...');
    writeFileSync(redirectsPath, '/*    /index.html   200\n');
  }

  // Verify Firebase config is present
  const indexContent = readFileSync(indexPath, 'utf8');
  if (!indexContent.includes('firebase')) {
    console.warn('⚠️  Warning: Firebase configuration may not be properly included');
  }

  console.log('✅ Build verification completed\n');

  // Step 5: Display build summary
  console.log('📊 Build Summary:');
  execSync('ls -la dist/', { stdio: 'inherit' });
  
  const stats = execSync('du -sh dist/', { encoding: 'utf8' });
  console.log(`📦 Total build size: ${stats.trim()}\n`);

  console.log('🎉 Build completed successfully!');
  console.log('📁 Your build is ready in the "dist" directory');
  console.log('🌐 You can now deploy the "dist" folder to Netlify\n');

  // Step 6: Deployment instructions
  console.log('📋 Deployment Instructions:');
  console.log('1. Zip the entire "dist" folder');
  console.log('2. Go to Netlify dashboard');
  console.log('3. Drag and drop the zip file to deploy');
  console.log('4. Or use Netlify CLI: netlify deploy --prod --dir=dist\n');

} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}