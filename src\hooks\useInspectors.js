import { useState, useEffect } from 'react';
import {
  createInspector,
  getInspectorsFromFirestore,
  getInspectorById,
  updateInspector,
  deleteInspector,
  toggleInspectorStatus,
  resetInspectorPassword,
  getInspectorStatistics
} from '../services/inspectorService';

/**
 * Custom hook for managing inspectors data with Firebase Firestore
 * @returns {Object} Inspectors state and management functions
 */
export const useInspectors = () => {
  const [inspectors, setInspectors] = useState([]);
  const [statistics, setStatistics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [saving, setSaving] = useState(false);

  /**
   * Load inspectors from Firestore
   */
  const loadInspectors = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await getInspectorsFromFirestore();

      if (result.success) {
        // Filter out deleted inspectors for display
        const activeInspectors = result.data.filter(inspector => inspector.status !== 'deleted');
        setInspectors(activeInspectors);
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      console.error('Error loading inspectors:', err);
      setError(err.message || 'Failed to load inspectors');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Load inspector statistics
   */
  const loadStatistics = async () => {
    try {
      const result = await getInspectorStatistics();

      if (result.success) {
        setStatistics(result.data);
      } else {
        console.error('Failed to load statistics:', result.message);
      }
    } catch (err) {
      console.error('Error loading statistics:', err);
    }
  };

  /**
   * Create a new inspector
   * @param {Object} inspectorData - Inspector data
   * @returns {Promise<Object>} Success status
   */
  const addInspector = async (inspectorData) => {
    try {
      setSaving(true);
      setError(null);

      const result = await createInspector(inspectorData);

      if (result.success) {
        // Add new inspector to local state
        setInspectors(prevInspectors => [result.data, ...prevInspectors]);

        // Reload statistics
        await loadStatistics();

        return { success: true, message: result.message };
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      console.error('Error creating inspector:', err);
      const errorMessage = err.message || 'Failed to create inspector';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setSaving(false);
    }
  };

  /**
   * Get a single inspector by ID
   * @param {string} inspectorId - Inspector ID
   * @returns {Promise<Object>} Inspector data or error
   */
  const getInspector = async (inspectorId) => {
    try {
      const result = await getInspectorById(inspectorId);

      if (result.success) {
        return { success: true, data: result.data };
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      console.error('Error getting inspector:', err);
      return { success: false, message: err.message || 'Failed to get inspector' };
    }
  };

  /**
   * Update an existing inspector
   * @param {string} inspectorId - Inspector ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} Success status
   */
  const updateInspectorData = async (inspectorId, updateData) => {
    try {
      setSaving(true);
      setError(null);

      const result = await updateInspector(inspectorId, updateData);

      if (result.success) {
        // Update local state
        setInspectors(prevInspectors =>
          prevInspectors.map(inspector =>
            inspector.id === inspectorId
              ? { ...inspector, ...updateData }
              : inspector
          )
        );
        return { success: true, message: result.message };
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      console.error('Error updating inspector:', err);
      const errorMessage = err.message || 'Failed to update inspector';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setSaving(false);
    }
  };

  /**
   * Delete an inspector
   * @param {string} inspectorId - Inspector ID
   * @returns {Promise<Object>} Success status
   */
  const removeInspector = async (inspectorId) => {
    try {
      setSaving(true);
      setError(null);

      const result = await deleteInspector(inspectorId);

      if (result.success) {
        // Remove inspector from local state
        setInspectors(prevInspectors =>
          prevInspectors.filter(inspector => inspector.id !== inspectorId)
        );

        // Reload statistics
        await loadStatistics();

        return { success: true, message: result.message };
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      console.error('Error deleting inspector:', err);
      const errorMessage = err.message || 'Failed to delete inspector';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setSaving(false);
    }
  };

  /**
   * Toggle inspector active status
   * @param {string} inspectorId - Inspector ID
   * @param {boolean} isActive - Whether to activate or deactivate
   * @returns {Promise<Object>} Success status
   */
  const toggleStatus = async (inspectorId, isActive) => {
    try {
      setSaving(true);
      setError(null);

      const result = await toggleInspectorStatus(inspectorId, isActive);

      if (result.success) {
        // Update local state
        setInspectors(prevInspectors =>
          prevInspectors.map(inspector =>
            inspector.id === inspectorId
              ? { ...inspector, status: isActive ? 'active' : 'inactive' }
              : inspector
          )
        );

        // Reload statistics
        await loadStatistics();

        return { success: true, message: result.message };
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      console.error('Error toggling inspector status:', err);
      const errorMessage = err.message || 'Failed to update inspector status';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setSaving(false);
    }
  };

  /**
   * Reset inspector password
   * @param {string} inspectorId - Inspector ID
   * @param {string} newPassword - New password
   * @returns {Promise<Object>} Success status
   */
  const resetPassword = async (inspectorId, newPassword) => {
    try {
      setSaving(true);
      setError(null);

      const result = await resetInspectorPassword(inspectorId, newPassword);

      if (result.success) {
        // Update local state to show password reset
        setInspectors(prevInspectors =>
          prevInspectors.map(inspector =>
            inspector.id === inspectorId
              ? { ...inspector, requiresPasswordChange: true }
              : inspector
          )
        );

        return {
          success: true,
          message: result.message,
          data: result.data
        };
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      console.error('Error resetting inspector password:', err);
      const errorMessage = err.message || 'Failed to reset password';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setSaving(false);
    }
  };

  /**
   * Refresh inspectors from Firestore
   */
  const refreshInspectors = async () => {
    await loadInspectors();
    await loadStatistics();
  };

  /**
   * Search inspectors by name or email
   * @param {string} searchTerm - Search term
   * @returns {Array} Filtered inspectors
   */
  const searchInspectors = (searchTerm) => {
    if (!searchTerm) return inspectors;

    const term = searchTerm.toLowerCase();
    return inspectors.filter(inspector =>
      inspector.name.toLowerCase().includes(term) ||
      inspector.email.toLowerCase().includes(term) ||
      inspector.employeeId?.toLowerCase().includes(term) ||
      inspector.company?.toLowerCase().includes(term)
    );
  };

  /**
   * Filter inspectors by status
   * @param {string} status - Status to filter by
   * @returns {Array} Filtered inspectors
   */
  const filterByStatus = (status) => {
    if (!status || status === 'all') return inspectors;
    return inspectors.filter(inspector => inspector.status === status);
  };

  // Load inspectors and statistics on hook initialization
  useEffect(() => {
    loadInspectors();
    loadStatistics();
  }, []);

  return {
    // State
    inspectors,
    statistics,
    loading,
    error,
    saving,

    // Actions
    addInspector,
    getInspector,
    updateInspectorData,
    removeInspector,
    toggleStatus,
    resetPassword,
    refreshInspectors,

    // Utility functions
    searchInspectors,
    filterByStatus,

    // Direct state setters (for local state management)
    setInspectors,
    setError
  };
};