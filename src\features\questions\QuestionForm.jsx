import React, { useState } from 'react';

const TextInput = ({ label, name, value, onChange }) => (
  <div className="mb-4">
    <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor={name}>
      {label}
    </label>
    <input
      className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
      id={name}
      type="text"
      placeholder={label}
      name={name}
      value={value}
      onChange={onChange}
    />
  </div>
);

const RadioGroup = ({ label, name, options, value, onChange }) => (
  <div className="mb-4">
    <label className="block text-gray-700 text-sm font-bold mb-2">{label}</label>
    <div className="flex flex-wrap">
      {options.map(option => (
        <div key={option} className="mr-4 mb-2">
          <input
            type="radio"
            id={`${name}_${option}`}
            name={name}
            value={option}
            checked={value === option}
            onChange={onChange}
            className="mr-2"
          />
          <label htmlFor={`${name}_${option}`}>{option}</label>
        </div>
      ))}
    </div>
  </div>
);

const CheckboxGroup = ({ label, name, options, values, onChange }) => (
  <div className="mb-4">
    <label className="block text-gray-700 text-sm font-bold mb-2">{label}</label>
    <div className="flex flex-wrap">
      {options.map(option => (
        <div key={option} className="mr-4 mb-2">
          <input
            type="checkbox"
            id={`${name}_${option}`}
            name={name}
            value={option}
            checked={values.includes(option)}
            onChange={onChange}
            className="mr-2"
          />
          <label htmlFor={`${name}_${option}`}>{option}</label>
        </div>
      ))}
    </div>
  </div>
);

const YesNoUnknown = ({ label, name, value, onChange }) => (
  <RadioGroup
    label={label}
    name={name}
    options={['Yes', 'No', 'Unknown']}
    value={value}
    onChange={onChange}
  />
);

const Condition = ({ label, name, value, onChange }) => (
  <RadioGroup
    label={label}
    name={name}
    options={['Excellent', 'Good', 'Fair', 'Poor']}
    value={value}
    onChange={onChange}
  />
);

const initialQuestions = [
  { id: 'locationOfRisk', label: 'Location of risk', type: 'text' },
  { id: 'inspectionDate', label: 'Inspection Date', type: 'text' },
  { id: 'policyNumber', label: 'Policy Number', type: 'text' },
  { id: 'inspector', label: 'Inspector', type: 'text' },
  { id: 'contactAtLocation', label: 'Contact at location', type: 'text' },
  { id: 'policyType', label: 'Policy Type', type: 'text' },
  { id: 'contactPhone', label: 'Contact Phone #', type: 'text' },
  { id: 'dwellingType', label: 'Dwelling Type', type: 'radio', options: ['SFD', 'Condo', 'Mobile Home', 'Duplex', 'Multi-Family', 'Other'], hasOther: true },
  { id: 'roofType', label: 'Roof Type', type: 'radio', options: ['Tile', 'Comp', 'Shake', 'Flat', 'Metal', 'Other'], hasOther: true },
  { id: 'roofShape', label: 'Roof Shape', type: 'radio', options: ['Gable', 'Hip', 'Flat', 'Other'], hasOther: true },
  { id: 'yearRoofLastUpdated', label: 'Year Roof Last Updated', type: 'text' },
  { id: 'overallConditionOfRoof', label: 'Overall Condition of Roof', type: 'condition' },
  { id: 'electricalType', label: 'Electrical Type', type: 'radio', options: ['Circuit Breakers', 'Fuses', 'Unknown'] },
  { id: 'anyKnownAluminumWiring', label: 'Any known aluminum wiring?', type: 'yesnounknown' },
  { id: 'overallConditionOfElectrical', label: 'Overall Condition of Electrical', type: 'condition' },
  { id: 'yearElectricalLastUpdated', label: 'Year Electrical Last Updated', type: 'text' },
  { id: 'plumbingType', label: 'Plumbing Type', type: 'radio', options: ['Copper', 'Galvanized', 'PVC', 'PEX', 'Other'], hasOther: true },
  { id: 'overallConditionOfPlumbing', label: 'Overall Condition of Plumbing', type: 'condition' },
  { id: 'yearPlumbingLastUpdated', label: 'Year Plumbing Last Updated', type: 'text' },
  { id: 'heatingType', label: 'Heating Type (Check all that apply)', type: 'checkbox', options: ['Central', 'Wall', 'Space Heater', 'Floor Heater', 'Fireplace', 'Wood Stove', 'Other'], hasOther: true },
  { id: 'coolingType', label: 'Cooling Type (Check all that apply)', type: 'checkbox', options: ['Central', 'Evaporative Cooler', 'Window', 'None', 'Other'], hasOther: true },
  { id: 'overallConditionOfHeatingCooling', label: 'Overall Condition of Heating/Cooling Systems', type: 'condition' },
  { id: 'yearHeatingCoolingLastUpdated', label: 'Year Heating/Cooling Last Updated', type: 'text' },
  { id: 'propertyUpdatedRemodeled', label: 'Was the property updated/remodeled in the last 10 years?', type: 'yesnoexplain' },
  { id: 'visibleDamage', label: 'Any visible damage or deferred maintenance?', type: 'yesnoexplain' },
  { id: 'petsOnPremises', label: 'Any pets on premises?', type: 'yesnoexplain', explainLabel: 'If Yes, what type?' },
  { id: 'trampolinesPools', label: 'Any trampolines, pools, hot tubs, or playground equipment?', type: 'yesnoexplain' },
  { id: 'propertyFenced', label: 'Is property fenced?', type: 'yesno' },
  { id: 'fencingType', label: 'If Yes, type of fencing', type: 'text', dependsOn: 'propertyFenced', dependsOnValue: 'Yes' },
  { id: 'fencingInGoodCondition', label: 'Is fencing in good condition?', type: 'yesno', dependsOn: 'propertyFenced', dependsOnValue: 'Yes' },
  { id: 'businessOnPremises', label: 'Is there a business on the premises?', type: 'yesnoexplain' },
  { id: 'additionalStructures', label: 'Any additional structures on premises?', type: 'yesnoexplain', explainLabel: 'If Yes, list and describe' },
  { id: 'generalComments', label: 'General Comments', type: 'textarea' },
];

const generateInitialFormData = (questions) => {
  const formData = {};
  questions.forEach(q => {
    formData[q.id] = q.type === 'checkbox' ? [] : '';
    if (q.hasOther) formData[`${q.id}Other`] = '';
    if (q.type === 'yesnoexplain') formData[`${q.id}Explanation`] = '';
  });
  return formData;
};

const Question = ({ q, formData, handleChange }) => {
  if (q.dependsOn && formData[q.dependsOn] !== q.dependsOnValue) {
    return null;
  }

  const value = formData[q.id];

  switch (q.type) {
    case 'text':
      return <TextInput label={q.label} name={q.id} value={value} onChange={handleChange} />;
    case 'radio':
      return (
        <>
          <RadioGroup label={q.label} name={q.id} options={q.options} value={value} onChange={handleChange} />
          {q.hasOther && value === 'Other' && (
            <TextInput label={`Other ${q.label}`} name={`${q.id}Other`} value={formData[`${q.id}Other`]} onChange={handleChange} />
          )}
        </>
      );
    case 'checkbox':
       return (
        <>
          <CheckboxGroup label={q.label} name={q.id} options={q.options} values={value} onChange={handleChange} />
          {q.hasOther && value.includes('Other') && (
            <TextInput label={`Other ${q.label}`} name={`${q.id}Other`} value={formData[`${q.id}Other`]} onChange={handleChange} />
          )}
        </>
      );
    case 'condition':
      return <Condition label={q.label} name={q.id} value={value} onChange={handleChange} />;
    case 'yesnounknown':
      return <YesNoUnknown label={q.label} name={q.id} value={value} onChange={handleChange} />;
    case 'yesno':
       return <RadioGroup label={q.label} name={q.id} options={['Yes', 'No']} value={value} onChange={handleChange} />;
    case 'yesnoexplain':
      return (
        <>
          <RadioGroup label={q.label} name={q.id} options={['Yes', 'No']} value={value} onChange={handleChange} />
          {value === 'Yes' && (
            <TextInput label={q.explainLabel || 'If Yes, explain'} name={`${q.id}Explanation`} value={formData[`${q.id}Explanation`]} onChange={handleChange} />
          )}
        </>
      );
    case 'textarea':
      return (
        <div className="mb-4">
          <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor={q.id}>{q.label}</label>
          <textarea
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            id={q.id}
            placeholder={q.label}
            name={q.id}
            value={value}
            onChange={handleChange}
            rows="4"
          />
        </div>
      );
    default:
      return null;
  }
};

export default function QuestionForm() {
  const [questions, setQuestions] = useState(initialQuestions);
  const [formData, setFormData] = useState(generateInitialFormData(initialQuestions));
  const [isEditing, setIsEditing] = useState(false);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    if (type === 'checkbox') {
      setFormData(prev => ({
        ...prev,
        [name]: checked ? [...(prev[name] || []), value] : (prev[name] || []).filter(v => v !== value)
      }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleAddQuestion = () => {
    const newQuestion = { id: `newQuestion${Date.now()}`, label: 'New Question', type: 'text' };
    setQuestions([...questions, newQuestion]);
  };

  const handleDeleteQuestion = (id) => {
    setQuestions(questions.filter(q => q.id !== id));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log(formData);
    alert('Form data has been logged to the console.');
  };

  return (
    <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-4xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Inspection Questionnaire</h2>
        <button
          className="bg-brand-purple text-white hover:bg-brand-purple/80 focus:ring-2 focus:ring-brand-purple py-2 px-4 rounded focus:outline-none focus:shadow-outline"
          onClick={() => setIsEditing(!isEditing)}
        >
          {isEditing ? 'Done Editing' : 'Edit Questions'}
        </button>
      </div>

      {isEditing && (
        <div className="mb-6 p-4 border rounded-lg bg-gray-50">
          <h3 className="text-xl font-bold mb-4">Manage Questions</h3>
          {questions.map((q, index) => (
            <div key={q.id} className="flex items-center justify-between p-2 border-b">
              <span>{index + 1}. {q.label} ({q.type})</span>
              <div>
                <button className="text-blue-500 hover:underline mr-4">Edit</button>
                <button className="text-red-500 hover:underline" onClick={() => handleDeleteQuestion(q.id)}>Delete</button>
              </div>
            </div>
          ))}
          <button
            className="mt-4 bg-brand-purple text-white hover:bg-brand-purple/80 focus:ring-2 focus:ring-brand-purple py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            onClick={handleAddQuestion}
          >
            Add New Question
          </button>
        </div>
      )}

      {!isEditing && (
        <form onSubmit={handleSubmit}>
          {questions.map(q => (
            <Question key={q.id} q={q} formData={formData} handleChange={handleChange} />
          ))}
          <div className="flex items-center justify-center mt-6">
            <button
              className="bg-brand-purple text-white hover:bg-brand-purple/80 focus:ring-2 focus:ring-brand-purple py-2 px-4 rounded focus:outline-none focus:shadow-outline"
              type="submit"
            >
              Submit
            </button>
          </div>
        </form>
      )}
    </div>
  );
} 